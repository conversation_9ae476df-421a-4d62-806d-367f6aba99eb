# LC Workflow Backend API Documentation for Flutter Developers

## Overview
This document provides comprehensive API integration details for Flutter developers working with the LC Workflow backend system. The backend is built with FastAPI and provides RESTful endpoints for loan application management.

## Base Configuration
- **Base URL**: `https://your-api-domain.com/api/v1/`
- **Authentication**: Bearer <PERSON> (JWT)
- **Content-Type**: `application/json`
- **File Upload**: `multipart/form-data`

## Authentication & Headers
All authenticated requests require:
```dart
headers: {
  'Authorization': 'Bearer YOUR_ACCESS_TOKEN',
  'Content-Type': 'application/json',
}
```

## Data Models for Flutter

### User Model
```dart
class User {
  final String id;
  final String username;
  final String email;
  final String firstName;
  final String lastName;
  final String? phoneNumber;
  final String role; // 'admin', 'manager', 'officer'
  final String status; // 'active', 'inactive'
  final String? departmentId;
  final String? branchId;
  final String? profileImageUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastLoginAt;

  User.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        username = json['username'],
        email = json['email'],
        firstName = json['first_name'],
        lastName = json['last_name'],
        phoneNumber = json['phone_number'],
        role = json['role'],
        status = json['status'],
        departmentId = json['department_id'],
        branchId = json['branch_id'],
        profileImageUrl = json['profile_image_url'],
        createdAt = DateTime.parse(json['created_at']),
        updatedAt = DateTime.parse(json['updated_at']),
        lastLoginAt = json['last_login_at'] != null ? DateTime.parse(json['last_login_at']) : null;
}
```

### Application Model
```dart
class CustomerApplication {
  final String id;
  final String userId;
  final String status; // 'draft', 'pending', 'under_review', 'approved', 'rejected'
  
  // Borrower Information
  final String? idCardType;
  final String? idNumber;
  final String? fullNameKhmer;
  final String? fullNameLatin;
  final String? phone;
  final String? dateOfBirth;
  final String? portfolioOfficerName;
  
  // Address Information
  final String? currentAddress;
  final String? province;
  final String? district;
  final String? commune;
  final String? village;
  
  // Employment Information
  final String? occupation;
  final String? employerName;
  final double? monthlyIncome;
  final String? incomeSource;
  
  // Loan Details
  final double? requestedAmount;
  final List<dynamic>? loanPurposes;
  final String? purposeDetails;
  final String? productType;
  final String? desiredLoanTerm;
  final String? requestedDisbursementDate;
  final double? interestRate;
  
  // Guarantor Information
  final String? guarantorName;
  final String? guarantorPhone;
  final String? guarantorIdNumber;
  final String? guarantorAddress;
  final String? guarantorRelationship;
  
  // Financial Information
  final List<dynamic>? existingLoans;
  final double? monthlyExpenses;
  final double? assetsValue;
  
  // Risk Assessment
  final int? creditScore;
  final String? riskCategory; // 'low', 'medium', 'high'
  final String? assessmentNotes;
  
  // Workflow
  final String? workflowStage;
  final String? assignedReviewer;
  final String priorityLevel; // 'low', 'normal', 'high', 'urgent'
  
  // Timestamps
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? submittedAt;
  final DateTime? approvedAt;
  final String? approvedBy;
  final DateTime? rejectedAt;
  final String? rejectedBy;
  final String? rejectionReason;

  CustomerApplication.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        userId = json['user_id'],
        status = json['status'],
        idCardType = json['id_card_type'],
        idNumber = json['id_number'],
        fullNameKhmer = json['full_name_khmer'],
        fullNameLatin = json['full_name_latin'],
        phone = json['phone'],
        dateOfBirth = json['date_of_birth'],
        portfolioOfficerName = json['portfolio_officer_name'],
        currentAddress = json['current_address'],
        province = json['province'],
        district = json['district'],
        commune = json['commune'],
        village = json['village'],
        occupation = json['occupation'],
        employerName = json['employer_name'],
        monthlyIncome = json['monthly_income']?.toDouble(),
        incomeSource = json['income_source'],
        requestedAmount = json['requested_amount']?.toDouble(),
        loanPurposes = json['loan_purposes'] != null ? List.from(json['loan_purposes']) : null,
        purposeDetails = json['purpose_details'],
        productType = json['product_type'],
        desiredLoanTerm = json['desired_loan_term'],
        requestedDisbursementDate = json['requested_disbursement_date'],
        interestRate = json['interest_rate']?.toDouble(),
        guarantorName = json['guarantor_name'],
        guarantorPhone = json['guarantor_phone'],
        guarantorIdNumber = json['guarantor_id_number'],
        guarantorAddress = json['guarantor_address'],
        guarantorRelationship = json['guarantor_relationship'],
        existingLoans = json['existing_loans'],
        monthlyExpenses = json['monthly_expenses']?.toDouble(),
        assetsValue = json['assets_value']?.toDouble(),
        creditScore = json['credit_score'],
        riskCategory = json['risk_category'],
        assessmentNotes = json['assessment_notes'],
        workflowStage = json['workflow_stage'],
        assignedReviewer = json['assigned_reviewer'],
        priorityLevel = json['priority_level'] ?? 'normal',
        createdAt = DateTime.parse(json['created_at']),
        updatedAt = DateTime.parse(json['updated_at']),
        submittedAt = json['submitted_at'] != null ? DateTime.parse(json['submitted_at']) : null,
        approvedAt = json['approved_at'] != null ? DateTime.parse(json['approved_at']) : null,
        approvedBy = json['approved_by'],
        rejectedAt = json['rejected_at'] != null ? DateTime.parse(json['rejected_at']) : null,
        rejectedBy = json['rejected_by'],
        rejectionReason = json['rejection_reason'];
}
```

### File Model
```dart
class FileUpload {
  final String id;
  final String filename;
  final String originalFilename;
  final int fileSize;
  final String mimeType;
  final String filePath;
  final String uploadedBy;
  final String? applicationId;
  final DateTime createdAt;

  FileUpload.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        filename = json['filename'],
        originalFilename = json['original_filename'],
        fileSize = json['file_size'],
        mimeType = json['mime_type'],
        filePath = json['file_path'],
        uploadedBy = json['uploaded_by'],
        applicationId = json['application_id'],
        createdAt = DateTime.parse(json['created_at']);
}
```

## API Endpoints with Flutter Examples

### 1. Authentication Endpoints

#### Login
```dart
Future<AuthResponse> login(String username, String password) async {
  final response = await http.post(
    Uri.parse('$baseUrl/auth/login'),
    body: {
      'username': username,
      'password': password,
    },
  );
  
  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    return AuthResponse(
      accessToken: data['access_token'],
      refreshToken: data['refresh_token'],
      user: User.fromJson(data['user']),
    );
  } else {
    throw Exception('Login failed: ${response.body}');
  }
}
```

#### Refresh Token
```dart
Future<String> refreshToken(String refreshToken) async {
  final response = await http.post(
    Uri.parse('$baseUrl/auth/refresh'),
    headers: {'Content-Type': 'application/json'},
    body: jsonEncode({'refresh_token': refreshToken}),
  );
  
  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    return data['access_token'];
  } else {
    throw Exception('Token refresh failed');
  }
}
```

#### Get Current User
```dart
Future<User> getCurrentUser(String token) async {
  final response = await http.get(
    Uri.parse('$baseUrl/auth/me'),
    headers: {'Authorization': 'Bearer $token'},
  );
  
  if (response.statusCode == 200) {
    return User.fromJson(jsonDecode(response.body));
  } else {
    throw Exception('Failed to get user profile');
  }
}
```

### 2. Application Management

#### Create Application
```dart
Future<CustomerApplication> createApplication(
  Map<String, dynamic> applicationData,
  String token
) async {
  final response = await http.post(
    Uri.parse('$baseUrl/applications'),
    headers: {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    },
    body: jsonEncode(applicationData),
  );
  
  if (response.statusCode == 201) {
    return CustomerApplication.fromJson(jsonDecode(response.body));
  } else {
    throw Exception('Failed to create application');
  }
}
```

#### Get Applications with Filters
```dart
Future<PaginatedResponse<CustomerApplication>> getApplications({
  String? status,
  String? search,
  String? riskCategory,
  double? amountMin,
  double? amountMax,
  int page = 1,
  int size = 20,
  required String token,
}) async {
  final params = {
    'page': page.toString(),
    'size': size.toString(),
    if (status != null) 'status': status,
    if (search != null) 'search': search,
    if (riskCategory != null) 'risk_category': riskCategory,
    if (amountMin != null) 'amount_min': amountMin.toString(),
    if (amountMax != null) 'amount_max': amountMax.toString(),
  };
  
  final uri = Uri.parse('$baseUrl/applications').replace(queryParameters: params);
  final response = await http.get(
    uri,
    headers: {'Authorization': 'Bearer $token'},
  );
  
  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    return PaginatedResponse(
      items: (data['items'] as List).map((e) => CustomerApplication.fromJson(e)).toList(),
      total: data['total'],
      page: data['page'],
      size: data['size'],
      pages: data['pages'],
    );
  } else {
    throw Exception('Failed to fetch applications');
  }
}
```

#### Update Application Status
```dart
Future<void> updateApplicationStatus(
  String applicationId,
  String newStatus,
  String token,
  {String? rejectionReason}
) async {
  final body = {'status': newStatus};
  if (rejectionReason != null) {
    body['rejection_reason'] = rejectionReason;
  }
  
  final response = await http.put(
    Uri.parse('$baseUrl/applications/$applicationId/status'),
    headers: {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    },
    body: jsonEncode(body),
  );
  
  if (response.statusCode != 200) {
    throw Exception('Failed to update status');
  }
}
```

### 3. File Management

#### Upload File
```dart
Future<FileUpload> uploadFile({
  required File file,
  required String applicationId,
  required String documentType,
  required String token,
}) async {
  final request = http.MultipartRequest(
    'POST',
    Uri.parse('$baseUrl/files/upload'),
  );
  
  request.headers['Authorization'] = 'Bearer $token';
  request.files.add(await http.MultipartFile.fromPath('file', file.path));
  request.fields.addAll({
    'application_id': applicationId,
    'document_type': documentType,
  });
  
  final response = await request.send();
  final responseBody = await response.stream.bytesToString();
  
  if (response.statusCode == 201) {
    return FileUpload.fromJson(jsonDecode(responseBody));
  } else {
    throw Exception('File upload failed');
  }
}
```

#### Download File
```dart
Future<Uint8List> downloadFile(String fileId, String token) async {
  final response = await http.get(
    Uri.parse('$baseUrl/files/$fileId/download'),
    headers: {'Authorization': 'Bearer $token'},
  );
  
  if (response.statusCode == 200) {
    return response.bodyBytes;
  } else {
    throw Exception('File download failed');
  }
}
```

### 4. Department Management

#### Get Departments
```dart
Future<List<Department>> getDepartments(String token) async {
  final response = await http.get(
    Uri.parse('$baseUrl/departments'),
    headers: {'Authorization': 'Bearer $token'},
  );
  
  if (response.statusCode == 200) {
    final List<dynamic> data = jsonDecode(response.body);
    return data.map((e) => Department.fromJson(e)).toList();
  } else {
    throw Exception('Failed to fetch departments');
  }
}
```

### 5. Branch Management

#### Get Branches
```dart
Future<List<Branch>> getBranches(String token) async {
  final response = await http.get(
    Uri.parse('$baseUrl/branches'),
    headers: {'Authorization': 'Bearer $token'},
  );
  
  if (response.statusCode == 200) {
    final List<dynamic> data = jsonDecode(response.body);
    return data.map((e) => Branch.fromJson(e)).toList();
  } else {
    throw Exception('Failed to fetch branches');
  }
}
```

### 6. Dashboard

#### Get Dashboard Statistics
```dart
class DashboardStats {
  final int totalApplications;
  final int pendingApplications;
  final int approvedApplications;
  final int rejectedApplications;
  final double totalLoanAmount;
  final Map<String, int> applicationsByStatus;

  DashboardStats.fromJson(Map<String, dynamic> json)
      : totalApplications = json['total_applications'],
        pendingApplications = json['pending_applications'],
        approvedApplications = json['approved_applications'],
        rejectedApplications = json['rejected_applications'],
        totalLoanAmount = json['total_loan_amount']?.toDouble() ?? 0.0,
        applicationsByStatus = Map<String, int>.from(json['applications_by_status']);
}

Future<DashboardStats> getDashboardStats(String token) async {
  final response = await http.get(
    Uri.parse('$baseUrl/dashboard/stats'),
    headers: {'Authorization': 'Bearer $token'},
  );
  
  if (response.statusCode == 200) {
    return DashboardStats.fromJson(jsonDecode(response.body));
  } else {
    throw Exception('Failed to fetch dashboard stats');
  }
}
```

## Error Handling in Flutter

### Custom Exception Handler
```dart
class ApiException implements Exception {
  final int statusCode;
  final String message;
  final Map<String, dynamic>? details;

  ApiException(this.statusCode, this.message, [this.details]);

  @override
  String toString() => 'ApiException: $statusCode - $message';
}

void handleApiError(http.Response response) {
  final data = jsonDecode(response.body);
  final message = data['detail'] ?? data['message'] ?? 'Unknown error';
  
  switch (response.statusCode) {
    case 400:
      throw ApiException(400, 'Bad Request: $message', data);
    case 401:
      throw ApiException(401, 'Unauthorized: Please login again');
    case 403:
      throw ApiException(403, 'Forbidden: Insufficient permissions');
    case 404:
      throw ApiException(404, 'Not Found: Resource does not exist');
    case 422:
      throw ApiException(422, 'Validation Error: ${data['detail']}', data);
    default:
      throw ApiException(response.statusCode, 'Server Error: $message');
  }
}
```

## Pagination Helper
```dart
class PaginatedResponse<T> {
  final List<T> items;
  final int total;
  final int page;
  final int size;
  final int pages;

  PaginatedResponse({
    required this.items,
    required this.total,
    required this.page,
    required this.size,
    required this.pages,
  });
}
```

## Common Parameters Reference

### Application Status Values
- `draft` - Initial draft state
- `pending` - Submitted and waiting for review
- `under_review` - Currently being reviewed
- `approved` - Application approved
- `rejected` - Application rejected

### Risk Category Values
- `low` - Low risk borrower
- `medium` - Medium risk borrower
- `high` - High risk borrower

### Priority Level Values
- `low` - Low priority processing
- `normal` - Standard processing
- `high` - High priority processing
- `urgent` - Urgent priority processing

### User Role Values
- `admin` - Full system access
- `manager` - Department/Branch management
- `officer` - Loan officer access

## File Upload Specifications

### Supported File Types
- **Images**: JPG, JPEG, PNG, WebP (max 10MB)
- **Documents**: PDF, DOC, DOCX (max 10MB)
- **Archives**: ZIP, RAR (max 10MB)

### Required Metadata
```dart
{
  "application_id": "uuid-string",
  "document_type": "borrower_id|guarantor_id|income_proof|property_docs|other"
}
```

## Testing Your Integration

### Test Authentication
```dart
// Test login
final testUser = await login('test_officer', 'test123');
print('Login successful: ${testUser.user.username}');

// Test token refresh
final newToken = await refreshToken(testUser.refreshToken);
print('Token refreshed: ${newToken.substring(0, 20)}...');
```

### Test Application Creation
```dart
final testApplication = {
  'full_name_khmer': 'ឈ្មោះខ្មែរ',
  'full_name_latin': 'Test User',
  'phone': '012345678',
  'requested_amount': 5000.0,
  'product_type': 'personal_loan',
  'purpose_details': 'Business expansion'
};

final createdApp = await createApplication(testApplication, newToken);
print('Application created: ${createdApp.id}');
```

## Environment Configuration

### Development
```dart
const String baseUrl = 'http://localhost:8000/api/v1';
```

### Production
```dart
const String baseUrl = 'https://your-production-domain.com/api/v1';
```

## Rate Limiting
- **Standard endpoints**: 100 requests per minute
- **File uploads**: 10 requests per minute
- **Authentication**: 5 attempts per minute

Headers to check:
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset timestamp

## Support & Troubleshooting

### Common Issues
1. **401 Unauthorized**: Check token validity and expiration
2. **403 Forbidden**: Verify user role permissions
3. **422 Validation Error**: Check required fields and data types
4. **500 Internal Error**: Contact backend team

### Debug Mode
Enable debug logging:
```dart
class ApiService {
  static bool debugMode = true;
  
  static void logRequest(String method, String url, [dynamic body]) {
    if (debugMode) {
      print('API Request: $method $url');
      if (body != null) print('Body: $body');
    }
  }
}
```

## Contact Information
- **Backend Team**: <EMAIL>
- **API Documentation**: https://your-docs-url.com
- **Issue Tracker**: https://github.com/your-org/lc-workflow-backend/issues