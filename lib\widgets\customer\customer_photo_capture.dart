import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

/// IMPORTANT: Do NOT place this widget inside a [SingleChildScrollView] or any other vertical scrollable parent.
/// Doing so will prevent the PageView from receiving horizontal swipe gestures.
class CustomerPhotoCapture extends StatefulWidget {
  final String title;
  final List<File> imageFiles;
  final Function(List<File>) onImagesChanged;
  final bool isSelfie;
  const CustomerPhotoCapture({
    super.key,
    required this.title,
    required this.imageFiles,
    required this.onImagesChanged,
    required this.isSelfie,
  });

  @override
  State<CustomerPhotoCapture> createState() => _CustomerPhotoCaptureState();
}

class _CustomerPhotoCaptureState extends State<CustomerPhotoCapture>
    with SingleTickerProviderStateMixin {
  late PageController _pageController;
  late int _currentPage;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _currentPage = 0;
    _pageController = PageController(
      viewportFraction: 0.8,
      initialPage: _currentPage,
    );

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _pageController.addListener(_onPageChanged);
  }

  void _onPageChanged() {
    if (_pageController.page?.round() != _currentPage) {
      setState(() {
        _currentPage = _pageController.page?.round() ?? 0;
      });
    }
  }

  @override
  void dispose() {
    _pageController.removeListener(_onPageChanged);
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _pickImage(BuildContext context) async {
    final picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.camera,
      preferredCameraDevice:
          widget.isSelfie ? CameraDevice.front : CameraDevice.rear,
      imageQuality: 90,
    );
    if (image != null) {
      final newList = List<File>.from(widget.imageFiles)..add(File(image.path));
      widget.onImagesChanged(newList);
    }
  }

  void _removeImage(int index) {
    final newList = List<File>.from(widget.imageFiles)..removeAt(index);
    widget.onImagesChanged(newList);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.title,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 180,
          child: GestureDetector(
            onHorizontalDragStart: (details) {
              debugPrint('Horizontal drag started');
            },
            onHorizontalDragUpdate: (details) {
              debugPrint(
                'Horizontal drag update: [33m[1m[4m[7m${details.primaryDelta}[0m',
              );
            },
            onHorizontalDragEnd: (details) {
              debugPrint('Horizontal drag ended');
            },
            child: PageView.builder(
              controller: _pageController,
              physics: const BouncingScrollPhysics(),
              itemCount: widget.imageFiles.length + 1,
              itemBuilder: (context, index) {
                return AnimatedBuilder(
                  animation: _pageController,
                  builder: (context, child) {
                    double value = 1.0;
                    if (_pageController.hasClients &&
                        _pageController.page != null) {
                      value =
                          1 -
                          (((_pageController.page ?? _currentPage) - index)
                                      .abs() *
                                  0.1)
                              .clamp(0.0, 1.0);
                    } else if (_currentPage != index) {
                      value = 0.9;
                    }
                    return Transform.scale(scale: value, child: child);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8.0,
                      horizontal: 2.0,
                    ),
                    child:
                        (index == widget.imageFiles.length)
                            ? GestureDetector(
                              onTap: () => _pickImage(context),
                              child: Container(
                                width: 180,
                                height: 180,
                                decoration: BoxDecoration(
                                  color: Colors.grey[100],
                                  border: Border.all(color: Colors.grey[300]!),
                                  borderRadius: BorderRadius.circular(16),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(
                                        alpha: (255 * 0.04).roundToDouble(),
                                        red: Colors.black.r,
                                        green: Colors.black.g,
                                        blue: Colors.black.b,
                                      ),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: const Icon(
                                  Icons.add_a_photo,
                                  size: 48,
                                  color: Colors.grey,
                                ),
                              ),
                            )
                            : Stack(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(16),
                                  child: Image.file(
                                    widget.imageFiles[index],
                                    fit: BoxFit.cover,
                                    width: 180,
                                    height: 180,
                                  ),
                                ),
                                Positioned(
                                  top: 8,
                                  right: 8,
                                  child: GestureDetector(
                                    onTap: () => _removeImage(index),
                                    child: Container(
                                      padding: const EdgeInsets.all(6),
                                      decoration: BoxDecoration(
                                        color: Colors.black54,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.close,
                                        color: Colors.white,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 8),
        if (widget.imageFiles.isNotEmpty)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(widget.imageFiles.length, (index) {
              return GestureDetector(
                onTap: () {
                  _pageController.animateToPage(
                    index,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: _currentPage == index ? 24.0 : 12.0,
                  height: 12.0,
                  margin: const EdgeInsets.symmetric(horizontal: 4.0),
                  decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    borderRadius: BorderRadius.circular(6.0),
                    color:
                        _currentPage == index
                            ? Theme.of(context).primaryColor
                            : Colors.grey[300],
                    border: Border.all(
                      color:
                          _currentPage == index
                              ? Theme.of(context).primaryColor
                              : Colors.grey[400]!,
                      width: 1.5,
                    ),
                  ),
                ),
              );
            }),
          ),
      ],
    );
  }
}
