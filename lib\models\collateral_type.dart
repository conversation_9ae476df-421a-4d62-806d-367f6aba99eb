enum CollateralType {
  nid('អត្តសញ្ញាណប័ណ្ឌ'),
  homePhoto('ផ្ទះ អតិថិជន'),
  businessPhoto('អាជីវកម្ម អតិថិជន'), // Borrower business photo
  hardTitle('ប្លង់ដី (រឹង)'),
  applicationForPossession('ពាក្យស្នើរសុំកាន់កាប់'),
  landConfirmationLetter('លិខិតបញ្ជាក់ដី'),
  transferOfPossessionLetter('លិខិតផ្ទេរសិទ្ធកាន់កាប់'),
  motorcycleCard('កាតគ្រីម៉ូតូ'),
  carCard('កាតគ្រីឡាន'),
  salePurchaseLetter('លិខិតទិញលក់'),
  guarantor('អ្នកធានា'),
  guarantorNID('ប័ណ្ឌអ្នកធានា'),
  guarantorBusinessPhoto('អាជីវកម្ម អ្នកធានា'), // Guarantor business photo
  otherProperty('ទ្រព្យផ្សេងៗ'),
  governmentOfficialCard('កាតមន្ត្រីរាជការ'),
  tractorConfirmationLetter('លិខិតបញ្ជាក់គោយន្ត'),
  guarantorHomePhoto('ផ្ទះ អ្នកធានា'),
  profilePhoto('រូបថតប្រវត្តិរូប'),
  coBorrower('អ្នករួមខ្ចី');

  const CollateralType(this.displayName);
  final String displayName;
}
