# LC Work Flow - API Integration Implementation

This document provides a comprehensive guide for the API integration implementation in the LC Work Flow Flutter application.

## Overview

The API integration transforms the app from a local-only storage system to a full-featured client-server application with:

- **User Authentication & Authorization** with JWT tokens
- **Role-based Access Control** (<PERSON><PERSON>, Manager, Officer, Viewer)
- **Offline-first Architecture** with automatic synchronization
- **Real-time Data Sync** when connectivity is available
- **File Upload/Download** capabilities
- **Comprehensive Error Handling** and retry mechanisms

## Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│   Flutter App   │────▶│   FastAPI       │────▶│   PostgreSQL    │
│   (Frontend)    │     │   Backend       │     │   Database      │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                        │                        │
         │                        │                        │
         ▼                        ▼                        ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│   SQLite        │     │   Redis Cache   │     │   File Storage  │
│   (Local DB)    │     │   (Sessions)    │     │   (Images/Docs) │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## New Dependencies Added

The following dependencies have been added to `pubspec.yaml`:

```yaml
dependencies:
  # API Integration
  dio: ^5.0.0                      # HTTP client for API calls
  connectivity_plus: ^4.0.0        # Network connectivity monitoring
  jwt_decoder: ^2.0.1              # JWT token handling
  flutter_secure_storage: ^9.0.0   # Secure storage for tokens
  retry: ^3.1.1                    # Retry logic for failed requests
  synchronized: ^3.1.0             # Thread synchronization
  workmanager: ^0.5.1              # Background task scheduling
  equatable: ^2.0.5                # Value equality for models
```

## Key Components

### 1. Models

#### User Management Models
- **`User`** (`lib/models/user_model.dart`) - User account with roles and permissions
- **`Department`** (`lib/models/department_model.dart`) - Organizational departments
- **`Branch`** (`lib/models/branch_model.dart`) - Office branches/locations

#### Enhanced Customer Model
- Updated `Customer` model with sync status tracking
- Added `serverId` and `syncStatus` fields
- JSON serialization for API communication

### 2. Services

#### API Service (`lib/services/api_service.dart`)
- Centralized HTTP client using Dio
- Automatic token refresh and retry logic
- Request/response interceptors
- Error handling and exception mapping

#### Authentication Service (`lib/services/auth_service.dart`)
- JWT-based authentication
- Secure token storage
- User profile management
- Password change functionality

#### Connectivity Service (`lib/services/connectivity_service.dart`)
- Network status monitoring
- Automatic sync triggering when online
- Connection quality detection
- Offline/online state management

### 3. Repository Pattern

#### Application Repository (`lib/repositories/application_repository.dart`)
- Abstraction layer between UI and data sources
- Offline-first data access
- Automatic synchronization logic
- Conflict resolution handling

### 4. Configuration

#### API Configuration (`lib/core/config/api_config.dart`)
- Centralized API endpoints and settings
- Timeout and retry configurations
- Environment-specific settings

## User Roles and Permissions

### Role Hierarchy
1. **Admin** - Full system access
   - Manage users, departments, branches
   - Approve/reject all applications
   - System configuration

2. **Manager** - Department/branch management
   - Approve/reject applications in their scope
   - Manage officers in their department
   - View all applications in their branch

3. **Officer** - Application processing
   - Create and edit applications
   - Submit applications for approval
   - View their own applications

4. **Viewer** - Read-only access
   - View applications (limited scope)
   - Generate reports
   - No modification permissions

## Authentication Flow

### Login Process
1. User enters credentials
2. App sends login request to `/api/v1/auth/login`
3. Server validates credentials and returns JWT tokens
4. Tokens stored securely using `flutter_secure_storage`
5. User profile cached locally
6. App navigates to dashboard

### Token Management
- **Access Token**: Short-lived (1 hour), used for API requests
- **Refresh Token**: Long-lived (30 days), used to get new access tokens
- Automatic token refresh when access token expires
- Secure storage prevents token theft

### Logout Process
1. Call `/api/v1/auth/logout` to invalidate server session
2. Clear all local tokens and user data
3. Navigate back to login screen

## Data Synchronization Strategy

### Offline-First Approach
1. **Local Storage**: SQLite remains the primary data source for UI
2. **API Integration**: Server acts as the master data store
3. **Sync Queue**: Changes queued locally when offline
4. **Automatic Sync**: Triggered when connectivity restored

### Sync Status Tracking
Each application has a sync status:
- **`synced`**: Data matches server
- **`created`**: New application, needs upload
- **`updated`**: Modified application, needs sync
- **`pending`**: Queued for synchronization
- **`failed`**: Sync failed, needs retry

### Conflict Resolution
- **Last-write-wins**: Simple conflict resolution
- **Version tracking**: Future enhancement for complex conflicts
- **Manual resolution**: UI for handling conflicts

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/refresh` - Refresh tokens
- `GET /api/v1/auth/profile` - Get user profile
- `PUT /api/v1/auth/profile` - Update profile
- `POST /api/v1/auth/change-password` - Change password

### Applications
- `GET /api/v1/applications` - List applications (with pagination/filtering)
- `POST /api/v1/applications` - Create new application
- `GET /api/v1/applications/{id}` - Get specific application
- `PUT /api/v1/applications/{id}` - Update application
- `DELETE /api/v1/applications/{id}` - Delete application
- `POST /api/v1/applications/{id}/submit` - Submit for approval
- `POST /api/v1/applications/{id}/approve` - Approve application
- `POST /api/v1/applications/{id}/reject` - Reject application

### File Management
- `POST /api/v1/files/upload` - Upload files
- `GET /api/v1/files/{id}` - Download files
- `DELETE /api/v1/files/{id}` - Delete files

### User Management (Admin/Manager only)
- `GET /api/v1/users` - List users
- `POST /api/v1/users` - Create user
- `PUT /api/v1/users/{id}` - Update user
- `DELETE /api/v1/users/{id}` - Deactivate user

## Backend Implementation

### Technology Stack
- **FastAPI** - Modern Python web framework
- **PostgreSQL** - Primary database
- **Redis** - Session caching and real-time features
- **SQLAlchemy** - ORM for database operations
- **JWT** - Authentication tokens
- **Uvicorn** - ASGI server

### Database Schema
- **users** - User accounts and profiles
- **departments** - Organizational structure
- **branches** - Office locations
- **customer_applications** - Loan applications
- **files** - File metadata and storage

### Demo Users
The backend creates demo users for testing:
- `admin` / `admin123` (Admin role)
- `manager` / `manager123` (Manager role)
- `officer` / `officer123` (Officer role)
- `<EMAIL>` / `demo123` (Officer role)

## Setup Instructions

### 1. Flutter App Setup

1. **Update dependencies**:
   ```bash
   flutter pub get
   ```

2. **Configure API endpoint**:
   Update `lib/core/config/api_config.dart` with your backend URL:
   ```dart
   static const String baseUrl = 'http://your-api-server.com/api/v1';
   ```

3. **Run the app**:
   ```bash
   flutter run
   ```

### 2. Backend Setup

1. **Install Python dependencies**:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **Set up PostgreSQL**:
   ```sql
   CREATE DATABASE lc_workflow;
   ```

3. **Configure environment**:
   ```bash
   export DATABASE_URL="postgresql://postgres:password@localhost:5432/lc_workflow"
   export JWT_SECRET_KEY="your-secret-key"
   ```

4. **Run the backend**:
   ```bash
   python main.py
   ```

5. **Access API documentation**:
   - Swagger UI: `http://localhost:8000/docs`
   - ReDoc: `http://localhost:8000/redoc`

## Testing the Integration

### 1. Authentication Testing
1. Launch the app
2. Use demo credentials: `<EMAIL>` / `demo123`
3. Verify successful login and dashboard access
4. Test logout functionality

### 2. Application Sync Testing
1. Create applications while offline
2. Go online and verify automatic sync
3. Create applications while online
4. Test sync status indicators

### 3. Role-based Access Testing
1. Login with different user roles
2. Verify appropriate permissions and UI elements
3. Test approval/rejection workflows (Manager/Admin)

## Error Handling

### Network Errors
- Automatic retry with exponential backoff
- Graceful degradation to offline mode
- User-friendly error messages

### Authentication Errors
- Automatic token refresh
- Logout on authentication failure
- Clear error messaging

### Validation Errors
- Server-side validation error display
- Field-level error highlighting
- Form validation feedback

## Performance Considerations

### Caching Strategy
- User profile cached locally
- Application data cached with TTL
- Image caching for offline viewing

### Pagination
- Server-side pagination for large datasets
- Lazy loading for application lists
- Search and filtering capabilities

### File Handling
- Chunked file uploads for large files
- Resumable uploads for poor connections
- Image compression before upload

## Security Features

### Data Protection
- JWT tokens with short expiration
- Secure storage for sensitive data
- HTTPS-only communication

### Access Control
- Role-based permissions
- Resource-level authorization
- Audit logging for sensitive operations

### Input Validation
- Client-side validation for UX
- Server-side validation for security
- SQL injection prevention

## Monitoring and Logging

### Application Metrics
- API response times
- Error rates and types
- User activity tracking

### Sync Monitoring
- Sync success/failure rates
- Pending sync queue size
- Conflict resolution statistics

### Performance Monitoring
- Database query performance
- File upload/download speeds
- Memory and CPU usage

## Future Enhancements

### Real-time Features
- WebSocket integration for live updates
- Push notifications for approvals
- Real-time collaboration features

### Advanced Sync
- Operational transformation for conflicts
- Selective sync for large datasets
- Batch operations for efficiency

### Analytics
- Application processing metrics
- User behavior analytics
- Business intelligence dashboards

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Check network connectivity
   - Verify API server is running
   - Check firewall settings

2. **Authentication Failures**
   - Verify credentials
   - Check token expiration
   - Clear app data and re-login

3. **Sync Issues**
   - Check sync status indicators
   - Force manual sync
   - Review error logs

### Debug Mode
Enable debug logging by setting environment variable:
```bash
export FLUTTER_ENV=debug
```

### API Testing
Use the Swagger UI at `http://localhost:8000/docs` to test API endpoints directly.

## Support

For technical support or questions:
1. Check the API documentation at `/docs`
2. Review error logs in the app
3. Test with demo users first
4. Verify network connectivity

This implementation provides a robust foundation for your loan application workflow with modern authentication, offline capabilities, and scalable architecture.