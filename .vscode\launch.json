{"version": "0.2.0", "configurations": [{"name": "Debug FastAPI in Container", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "pathMappings": [{"localRoot": "${workspaceFolder}/backend", "remoteRoot": "/app"}], "justMyCode": false, "django": false, "subProcess": true}, {"name": "Debug FastAPI Local", "type": "python", "request": "launch", "program": "${workspaceFolder}/backend/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/backend", "env": {"PYTHONPATH": "${workspaceFolder}/backend", "DATABASE_URL": "postgresql://dev_user:dev_password@localhost:5432/lc_workflow_dev", "REDIS_URL": "redis://localhost:6379/0", "ENVIRONMENT": "development", "LOG_LEVEL": "debug"}, "args": [], "justMyCode": false}, {"name": "Run Tests", "type": "python", "request": "launch", "module": "pytest", "console": "integratedTerminal", "cwd": "${workspaceFolder}/backend", "env": {"PYTHONPATH": "${workspaceFolder}/backend", "DATABASE_URL": "postgresql://dev_user:dev_password@localhost:5432/lc_workflow_test", "REDIS_URL": "redis://localhost:6379/1", "ENVIRONMENT": "testing"}, "args": ["-v", "--tb=short", "tests/"], "justMyCode": false}]}