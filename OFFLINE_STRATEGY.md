# Offline-First Strategy for Flutter Application

This document outlines a comprehensive offline-first strategy for the Flutter application, enabling robust functionality even without a stable internet connection.

## 1. Proposed Offline-First Architecture

We recommend a repository-based architecture to abstract the data layer from the UI. This approach provides a clean separation of concerns and allows the application to seamlessly switch between local and remote data sources.

```mermaid
graph TD
    A[UI Layer] --> B{Repository}
    B --> C{Local Database (SQLite)}
    B --> D{Remote API}

    subgraph Data Layer
        C
        D
    end
```

**Components:**

*   **UI Layer:** <PERSON><PERSON>ins unaware of the data source. It requests data from and sends updates to the Repository.
*   **Repository:** The central point for data access. It decides whether to fetch data from the local SQLite database or the remote API. It also manages the data synchronization logic.
*   **Local Database (SQLite):** The single source of truth for the application's UI. All data displayed is read from here.
*   **Remote API:** The central server that stores the master data.

## 2. Data Synchronization Strategy

The synchronization strategy will ensure data consistency between the local database and the remote server.

### Synchronization Triggers:

*   **On App Launch:** A sync process will be initiated when the application starts.
*   **Periodic Sync:** A background process will attempt to sync data every 15 minutes.
*   **On-Demand:** A manual sync option will be available for the user.
*   **Network Status Change:** A sync will be triggered when the device comes online.

### Synchronization Flow:

1.  **Push Local Changes:**
    *   Identify all records in the local database marked with a `sync_status` of `created`, `updated`, or `deleted`.
    *   Batch these changes and send them to the remote API.
    *   For each successfully synced record, update its `sync_status` to `synced` and record the `server_id` if it's a new record.

2.  **Pull Remote Changes:**
    *   Request all records from the server that have been created or updated since the last successful sync timestamp.
    *   For each new or updated record from the server, insert or update the corresponding record in the local database.

## 3. Conflict Resolution

Data conflicts can occur when a record is modified both locally and on the server between syncs. We will use a **"last write wins"** strategy based on timestamps.

*   Each record will have an `updated_at` timestamp.
*   When a conflict is detected, the version with the most recent `updated_at` timestamp will be considered the correct one.
*   If the server's version is newer, the local record will be overwritten.
*   If the local version is newer, the local record will be pushed to the server.

## 4. Database Schema Modifications

To support the offline-first strategy, we need to add the following columns to the `customer_applications` table in `lib/services/database_helper.dart`:

```sql
CREATE TABLE customer_applications (
  -- Existing columns...
  id TEXT PRIMARY KEY,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'draft',
  
  -- New columns for offline support
  sync_status TEXT NOT NULL DEFAULT 'synced', -- (synced, created, updated, deleted)
  last_modified_locally TEXT,
  server_id TEXT
);
```

**New Columns:**

*   `sync_status`: Tracks the synchronization state of each record.
    *   `synced`: The record is in sync with the server.
    *   `created`: The record was created locally and has not been pushed to the server yet.
    *   `updated`: The record was updated locally and has not been pushed to the server yet.
*   `last_modified_locally`: An ISO 8601 timestamp that is updated whenever a local change is made. This is used for conflict resolution.
*   `server_id`: Stores the unique identifier from the remote server after a new record has been successfully synced.

## 5. API Changes

The backend API needs to be adjusted to support the offline-first strategy.

### Recommended Endpoints:

*   **`POST /api/sync/push`**
    *   **Description:** Allows the client to push a batch of created or updated records to the server.
    *   **Request Body:** An array of customer application objects.
    *   **Response:** An array of objects containing the `local_id` and the new `server_id` for each successfully created record.

*   **`GET /api/sync/pull`**
    *   **Description:** Allows the client to fetch all records that have been updated on the server since a given timestamp.
    *   **Query Parameters:** `last_sync_timestamp` (ISO 8601 format).
    *   **Response:** An array of customer application objects.