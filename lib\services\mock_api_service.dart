import 'dart:math';

/// Mock API service for testing without backend
/// This simulates API responses for development and testing
class MockApiService {
  static final MockApiService _instance = MockApiService._internal();
  factory MockApiService() => _instance;
  MockApiService._internal();

  // Simulate network delay
  Future<void> _simulateDelay() async {
    await Future.delayed(Duration(milliseconds: 500 + Random().nextInt(1000)));
  }

  // Mock users database
  final Map<String, Map<String, dynamic>> _mockUsers = {
    '<EMAIL>': {
      'id': '1',
      'username': '<EMAIL>',
      'email': '<EMAIL>',
      'password': 'demo123',
      'firstName': 'Demo',
      'lastName': 'User',
      'phoneNumber': '+855 12 345 678',
      'role': 'officer',
      'status': 'active',
      'departmentId': 'dept-1',
      'branchId': 'branch-1',
      'profileImageUrl': null,
      'createdAt':
          DateTime.now().subtract(Duration(days: 30)).toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
      'lastLoginAt': null,
    },
    'admin': {
      'id': '2',
      'username': 'admin',
      'email': '<EMAIL>',
      'password': 'admin123',
      'firstName': 'Admin',
      'lastName': 'User',
      'phoneNumber': '+855 12 111 111',
      'role': 'admin',
      'status': 'active',
      'departmentId': 'dept-1',
      'branchId': 'branch-1',
      'profileImageUrl': null,
      'createdAt':
          DateTime.now().subtract(Duration(days: 60)).toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
      'lastLoginAt': null,
    },
    'manager': {
      'id': '3',
      'username': 'manager',
      'email': '<EMAIL>',
      'password': 'manager123',
      'firstName': 'Manager',
      'lastName': 'User',
      'phoneNumber': '+855 12 222 222',
      'role': 'manager',
      'status': 'active',
      'departmentId': 'dept-1',
      'branchId': 'branch-1',
      'profileImageUrl': null,
      'createdAt':
          DateTime.now().subtract(Duration(days: 45)).toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
      'lastLoginAt': null,
    },
    'officer': {
      'id': '4',
      'username': 'officer',
      'email': '<EMAIL>',
      'password': 'officer123',
      'firstName': 'Loan',
      'lastName': 'Officer',
      'phoneNumber': '+855 12 333 333',
      'role': 'officer',
      'status': 'active',
      'departmentId': 'dept-1',
      'branchId': 'branch-1',
      'profileImageUrl': null,
      'createdAt':
          DateTime.now().subtract(Duration(days: 20)).toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
      'lastLoginAt': null,
    },
  };

  // Mock applications database
  final List<Map<String, dynamic>> _mockApplications = [];

  /// Mock login endpoint
  Future<Map<String, dynamic>> login(String username, String password) async {
    await _simulateDelay();

    final user = _mockUsers[username];
    if (user == null || user['password'] != password) {
      throw Exception('Invalid username or password');
    }

    // Update last login
    user['lastLoginAt'] = DateTime.now().toIso8601String();

    // Generate mock tokens
    final accessToken =
        'mock_access_token_${DateTime.now().millisecondsSinceEpoch}';
    final refreshToken =
        'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}';

    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'token_type': 'bearer',
      'expires_in': 3600,
      'user': Map<String, dynamic>.from(user)..remove('password'),
    };
  }

  /// Mock logout endpoint
  Future<Map<String, dynamic>> logout() async {
    await _simulateDelay();
    return {'message': 'Successfully logged out'};
  }

  /// Mock get profile endpoint
  Future<Map<String, dynamic>> getProfile(String username) async {
    await _simulateDelay();

    final user = _mockUsers[username];
    if (user == null) {
      throw Exception('User not found');
    }

    return Map<String, dynamic>.from(user)..remove('password');
  }

  /// Mock refresh token endpoint
  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    await _simulateDelay();

    // Simple validation - in real app, this would verify the token
    if (!refreshToken.startsWith('mock_refresh_token_')) {
      throw Exception('Invalid refresh token');
    }

    final newAccessToken =
        'mock_access_token_${DateTime.now().millisecondsSinceEpoch}';
    final newRefreshToken =
        'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}';

    return {
      'access_token': newAccessToken,
      'refresh_token': newRefreshToken,
      'token_type': 'bearer',
      'expires_in': 3600,
    };
  }

  /// Mock get applications endpoint
  Future<Map<String, dynamic>> getApplications({
    int page = 1,
    int limit = 20,
    String? status,
    String? userId,
  }) async {
    await _simulateDelay();

    // Generate some mock applications if empty
    if (_mockApplications.isEmpty) {
      _generateMockApplications();
    }

    var filteredApps = List<Map<String, dynamic>>.from(_mockApplications);

    // Apply filters
    if (status != null) {
      filteredApps =
          filteredApps.where((app) => app['status'] == status).toList();
    }
    if (userId != null) {
      filteredApps =
          filteredApps.where((app) => app['userId'] == userId).toList();
    }

    // Apply pagination
    final startIndex = (page - 1) * limit;
    final endIndex = startIndex + limit;
    final paginatedApps = filteredApps.sublist(
      startIndex,
      endIndex > filteredApps.length ? filteredApps.length : endIndex,
    );

    return {
      'applications': paginatedApps,
      'pagination': {
        'page': page,
        'limit': limit,
        'total': filteredApps.length,
        'pages': (filteredApps.length / limit).ceil(),
      },
    };
  }

  /// Mock create application endpoint
  Future<Map<String, dynamic>> createApplication(
    Map<String, dynamic> applicationData,
  ) async {
    await _simulateDelay();

    final application = {
      'id': 'app_${DateTime.now().millisecondsSinceEpoch}',
      'userId': applicationData['userId'] ?? '1',
      'status': 'pending',
      ...applicationData,
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    };

    _mockApplications.add(application);
    return application;
  }

  /// Mock get single application endpoint
  Future<Map<String, dynamic>> getApplication(String id) async {
    await _simulateDelay();

    final application = _mockApplications.firstWhere(
      (app) => app['id'] == id,
      orElse: () => throw Exception('Application not found'),
    );

    return application;
  }

  /// Mock update application endpoint
  Future<Map<String, dynamic>> updateApplication(
    String id,
    Map<String, dynamic> updateData,
  ) async {
    await _simulateDelay();

    final index = _mockApplications.indexWhere((app) => app['id'] == id);
    if (index == -1) {
      throw Exception('Application not found');
    }

    _mockApplications[index] = {
      ..._mockApplications[index],
      ...updateData,
      'updatedAt': DateTime.now().toIso8601String(),
    };

    return _mockApplications[index];
  }

  /// Generate mock applications for testing
  void _generateMockApplications() {
    final statuses = ['draft', 'pending', 'approved', 'rejected', 'disbursed'];
    final names = [
      {'khmer': 'សុខា ចាន', 'latin': 'Sokha Chan'},
      {'khmer': 'ដារា គឹម', 'latin': 'Dara Kim'},
      {'khmer': 'សុភា ទាន', 'latin': 'Sopha Tean'},
      {'khmer': 'វិចិត្រ ស៊ុន', 'latin': 'Vichit Sun'},
      {'khmer': 'ម៉ាលី ហេង', 'latin': 'Mali Heng'},
    ];

    for (int i = 0; i < 15; i++) {
      final name = names[i % names.length];
      final status = statuses[i % statuses.length];
      final createdDate = DateTime.now().subtract(Duration(days: i * 2));

      _mockApplications.add({
        'id': 'app_mock_$i',
        'userId': '1',
        'status': status,
        'idCardType': 'cambodianIdentity',
        'idNumber': '${100000000 + i}${Random().nextInt(99)}',
        'fullNameKhmer': name['khmer'],
        'fullNameLatin': name['latin'],
        'phone':
            '+855 ${10 + i} ${Random().nextInt(900) + 100} ${Random().nextInt(900) + 100}',
        'dateOfBirth':
            DateTime(
              1985 + (i % 20),
              (i % 12) + 1,
              (i % 28) + 1,
            ).toIso8601String(),
        'portfolioOfficerName': 'Demo Officer',
        'requestedAmount': (Random().nextInt(50) + 10) * 1000.0,
        'loanPurposes': ['commerce'],
        'purposeDetails': 'Business expansion and inventory purchase',
        'productType': 'monthly',
        'desiredLoanTerm': '${(i % 24) + 6} months',
        'requestedDisbursementDate':
            createdDate.add(Duration(days: 7)).toIso8601String(),
        'guarantorName': 'Guarantor ${i + 1}',
        'guarantorPhone':
            '+855 ${20 + i} ${Random().nextInt(900) + 100} ${Random().nextInt(900) + 100}',
        'collaterals': {},
        'documents': {},
        'createdAt': createdDate.toIso8601String(),
        'updatedAt': createdDate.toIso8601String(),
        'submittedAt':
            status != 'draft'
                ? createdDate.add(Duration(hours: 1)).toIso8601String()
                : null,
        'approvedAt':
            status == 'approved' || status == 'disbursed'
                ? createdDate.add(Duration(days: 1)).toIso8601String()
                : null,
        'rejectedAt':
            status == 'rejected'
                ? createdDate.add(Duration(days: 1)).toIso8601String()
                : null,
        'rejectionReason':
            status == 'rejected' ? 'Insufficient documentation' : null,
      });
    }
  }

  /// Mock file upload endpoint
  Future<Map<String, dynamic>> uploadFile(
    String filePath,
    String applicationId,
  ) async {
    await _simulateDelay();

    return {
      'file_id': 'file_${DateTime.now().millisecondsSinceEpoch}',
      'file_url': 'https://example.com/files/mock_file.jpg',
      'filename': filePath.split('/').last,
      'file_size': Random().nextInt(1000000) + 100000,
      'mime_type': 'image/jpeg',
    };
  }

  /// Check if mock API should be used (for development)
  static bool get useMockApi {
    // You can control this with environment variables or build configurations
    return true; // Set to false when real backend is ready
  }
}
