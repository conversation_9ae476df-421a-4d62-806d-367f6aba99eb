import 'package:get/get.dart';
import 'package:lc_work_flow/models/user_model.dart';
import 'package:lc_work_flow/models/customer_model.dart';
import 'package:lc_work_flow/services/auth_service.dart';
import 'package:lc_work_flow/services/local_storage_service.dart';
import 'package:lc_work_flow/services/simple_connectivity_service.dart';
import 'package:lc_work_flow/services/mock_api_service.dart';

class DashboardController extends GetxController {
  final AuthService _authService = AuthService();
  final LocalStorageService _localStorageService = LocalStorageService();

  // User data
  final currentUser = Rxn<User>();

  // Application data
  final applications = <Customer>[].obs;
  final isLoading = false.obs;
  final isRefreshing = false.obs;

  // Statistics
  final totalApplications = 0.obs;
  final pendingApplications = 0.obs;
  final approvedApplications = 0.obs;
  final rejectedApplications = 0.obs;

  // Sync status
  final syncStatus = 'idle'.obs;
  final lastSyncTime = Rxn<DateTime>();

  @override
  void onInit() {
    super.onInit();
    _loadUserData();
    _loadApplications();
    _updateStatistics();
  }

  void _loadUserData() {
    currentUser.value = _authService.currentUser;
  }

  Future<void> _loadApplications() async {
    isLoading.value = true;

    try {
      // Load from local storage
      final draftApps = await _localStorageService.getDraftApplications();
      final submittedApps =
          await _localStorageService.getSubmittedApplications();

      final allApps = [...draftApps, ...submittedApps];
      final customers =
          allApps
              .map((app) => _localStorageService.convertToCustomer(app))
              .toList();

      applications.assignAll(customers);
      _updateStatistics();
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load applications: ${e.toString()}',
        snackPosition: SnackPosition.TOP,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> refreshApplications() async {
    isRefreshing.value = true;

    try {
      await _loadApplications();
      _updateStatistics();

      Get.snackbar(
        'Refreshed',
        'Applications updated successfully',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to refresh applications: ${e.toString()}',
        snackPosition: SnackPosition.TOP,
      );
    } finally {
      isRefreshing.value = false;
    }
  }

  void _updateStatistics() {
    totalApplications.value = applications.length;
    pendingApplications.value =
        applications
            .where((app) => app.loanStatus == LoanStatus.pending)
            .length;
    approvedApplications.value =
        applications
            .where((app) => app.loanStatus == LoanStatus.approved)
            .length;
    rejectedApplications.value =
        applications
            .where((app) => app.loanStatus == LoanStatus.rejected)
            .length;
  }

  Future<void> syncApplications() async {
    if (!SimpleConnectivityService.to.isConnected) {
      Get.snackbar(
        'No Internet',
        'Cannot sync without internet connection',
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    syncStatus.value = 'syncing';

    try {
      // Simulate sync with mock API
      if (MockApiService.useMockApi) {
        await Future.delayed(Duration(seconds: 2)); // Simulate network delay

        lastSyncTime.value = DateTime.now();
        syncStatus.value = 'success';

        Get.snackbar(
          'Sync Complete',
          'Successfully synced with server',
          snackPosition: SnackPosition.TOP,
          duration: const Duration(seconds: 2),
        );
      } else {
        // Real API sync would go here
        throw Exception('Real API not implemented yet');
      }
    } catch (e) {
      syncStatus.value = 'failed';
      Get.snackbar(
        'Sync Error',
        'Failed to sync: ${e.toString()}',
        snackPosition: SnackPosition.TOP,
      );
    }
  }

  Future<void> logout() async {
    try {
      await _authService.logout();
      Get.offAllNamed('/login');
    } catch (e) {
      Get.snackbar(
        'Error',
        'Logout failed: ${e.toString()}',
        snackPosition: SnackPosition.TOP,
      );
    }
  }

  // Get applications by status
  List<Customer> getApplicationsByStatus(LoanStatus status) {
    return applications.where((app) => app.loanStatus == status).toList();
  }

  // Get recent applications (last 7 days)
  List<Customer> getRecentApplications() {
    final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
    return applications
        .where((app) => app.loanStartDate.isAfter(sevenDaysAgo))
        .toList();
  }

  // Get applications that need sync
  List<Customer> getPendingSyncApplications() {
    return applications
        .where(
          (app) =>
              app.syncStatus == 'pending' ||
              app.syncStatus == 'created' ||
              app.syncStatus == 'updated',
        )
        .toList();
  }

  // User role checks
  bool get canCreateApplications => currentUser.value?.role != UserRole.viewer;
  bool get canApproveApplications =>
      currentUser.value?.canApproveLoans ?? false;
  bool get canManageUsers => currentUser.value?.canManageUsers ?? false;

  String get welcomeMessage {
    final user = currentUser.value;
    if (user == null) return 'Welcome';

    final hour = DateTime.now().hour;
    String greeting;

    if (hour < 12) {
      greeting = 'Good Morning';
    } else if (hour < 17) {
      greeting = 'Good Afternoon';
    } else {
      greeting = 'Good Evening';
    }

    return '$greeting, ${user.firstName}';
  }
}
