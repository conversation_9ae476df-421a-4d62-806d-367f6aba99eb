import 'package:flutter/material.dart';
import 'package:lc_work_flow/models/document_type.dart';

class CustomerDocumentTypeDropdown extends StatelessWidget {
  final DocumentType? selectedDocumentType;
  final ValueChanged<DocumentType?> onChanged;

  const CustomerDocumentTypeDropdown({
    super.key,
    required this.selectedDocumentType,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<DocumentType>(
      value: selectedDocumentType,
      onChanged: onChanged,
      decoration: const InputDecoration(
        labelText: 'Document Type',
        border: OutlineInputBorder(),
        filled: true,
      ),
      items: DocumentType.values.map((DocumentType type) {
        return DropdownMenuItem<DocumentType>(
          value: type,
          child: Text(type.displayName),
        );
      }).toList(),
    );
  }
}
