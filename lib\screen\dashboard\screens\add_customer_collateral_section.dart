// import 'dart:io';
import 'package:flutter/material.dart';
import 'package:lc_work_flow/models/collateral_type.dart';
// import 'package:lc_work_flow/widgets/customer/customer_photo_capture.dart';

class CustomerCollateralSection extends StatelessWidget {
  final Set<CollateralType> selectedCollateralTypes;
  final Function(CollateralType, bool) onCollateralTypeChanged;

  const CustomerCollateralSection({
    super.key,
    required this.selectedCollateralTypes,
    required this.onCollateralTypeChanged,
  });

  @override
  Widget build(BuildContext context) {
    // Filter out default photo types that are captured automatically
    // NID, Home Photo, and Business Photo are required by default and don't need manual selection
    final selectableCollateralTypes =
        CollateralType.values
            .where(
              (type) =>
                  type != CollateralType.nid &&
                  type != CollateralType.homePhoto &&
                  type != CollateralType.businessPhoto,
            )
            .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'ប្រភេទធានា/Collateral Type',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        // Add info text about default photos
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'NID, Home Photo, and Business Photo are captured by default',
                  style: TextStyle(color: Colors.blue[700], fontSize: 12),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        ...selectableCollateralTypes.map(
          (type) => CheckboxListTile(
            value: selectedCollateralTypes.contains(type),
            title: Text(type.displayName),
            onChanged: (selected) {
              onCollateralTypeChanged(type, selected ?? false);
            },
          ),
        ),
        const SizedBox(height: 16),
        // Remove image capture widgets from step 2
      ],
    );
  }
}
