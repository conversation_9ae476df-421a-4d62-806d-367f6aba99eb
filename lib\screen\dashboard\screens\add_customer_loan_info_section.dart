import 'package:flutter/material.dart';
import 'package:lc_work_flow/models/product_type.dart' as product_type_dropdown;

class CustomerLoanInfoSection extends StatelessWidget {
  final TextEditingController requestedAmountController;
  final product_type_dropdown.ProductType? selectedProductType;
  final Function(product_type_dropdown.ProductType?) onProductTypeChanged;
  final TextEditingController loanTermController;

  const CustomerLoanInfoSection({
    super.key,
    required this.requestedAmountController,
    required this.selectedProductType,
    required this.onProductTypeChanged,
    required this.loanTermController,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: requestedAmountController,
          decoration: const InputDecoration(
            labelText: 'Requested Amount',
            hintText: 'Enter requested amount',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<product_type_dropdown.ProductType>(
          value: selectedProductType,
          onChanged: onProductTypeChanged,
          decoration: const InputDecoration(
            labelText: 'Product Type',
            border: OutlineInputBorder(),
            filled: true,
          ),
          items:
              product_type_dropdown.ProductType.values.map((type) {
                return DropdownMenuItem<product_type_dropdown.ProductType>(
                  value: type,
                  child: Text(type.displayName),
                );
              }).toList(),
        ),
        const SizedBox(height: 16),
        TextField(
          controller: loanTermController,
          decoration: const InputDecoration(
            labelText: 'Loan Term',
            hintText: 'Enter loan term (months)',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
        ),
      ],
    );
  }
}
