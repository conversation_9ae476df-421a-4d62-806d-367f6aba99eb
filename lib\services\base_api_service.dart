import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:lc_work_flow/core/config/api_config.dart';
import 'package:lc_work_flow/core/network/dio_client_factory.dart';
import 'package:lc_work_flow/core/utils/logger.dart';
import 'package:lc_work_flow/models/dto/application_dto.dart';
import 'package:lc_work_flow/models/dto/auth_response_dto.dart';
import 'package:lc_work_flow/models/dto/sync_status_dto.dart';
import 'package:lc_work_flow/services/interfaces/api_service_interface.dart';

/// Base implementation of the API service interface
class BaseApiService implements IApiService {
  late Dio _dio;
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final Connectivity _connectivity = Connectivity();

  BaseApiService() {
    initialize();
  }

  /// Initialize the API service with proper configuration
  void initialize() {
    _dio = DioClientFactory.createDio(secureStorage: _secureStorage);
    _setupTokenRefreshInterceptor();
  }

  /// Setup token refresh interceptor
  void _setupTokenRefreshInterceptor() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onError: (error, handler) async {
          // Handle 401 Unauthorized - Token expired
          if (error.response?.statusCode == 401 &&
              !error.requestOptions.path.contains('/auth/')) {
            final refreshed = await _refreshToken();
            if (refreshed) {
              // Retry the original request with new token
              final token = await _secureStorage.read(key: 'access_token');
              error.requestOptions.headers['Authorization'] = 'Bearer $token';
              try {
                final response = await _dio.fetch(error.requestOptions);
                handler.resolve(response);
                return;
              } catch (retryError) {
                // If retry fails, proceed with original error
              }
            }
          }
          handler.next(error);
        },
      ),
    );
  }

  /// Check if device is connected to the internet
  Future<bool> isConnected() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  /// Refresh authentication token
  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _secureStorage.read(key: 'refresh_token');
      if (refreshToken == null) return false;

      final response = await _dio.post(
        ApiConfig.refresh,
        data: {'refresh_token': refreshToken},
        options: Options(headers: {'Authorization': null}),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        await _secureStorage.write(
          key: 'access_token',
          value: data['access_token'],
        );
        if (data['refresh_token'] != null) {
          await _secureStorage.write(
            key: 'refresh_token',
            value: data['refresh_token'],
          );
        }
        return true;
      }
    } catch (e) {
      Logger.e('Token refresh failed', error: e);
    }
    return false;
  }

  // Generic HTTP methods

  /// Generic GET request
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Generic POST request
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Generic PUT request
  Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.put<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Generic DELETE request
  Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.delete<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // IApiService implementation

  @override
  Future<AuthResponseDto> login(String username, String password) async {
    try {
      final response = await post(
        ApiConfig.login,
        data: {'username': username, 'password': password},
      );
      return AuthResponseDto.fromJson(response.data);
    } catch (e) {
      Logger.e('Login failed', error: e);
      rethrow;
    }
  }

  @override
  Future<AuthResponseDto> refreshToken(String refreshToken) async {
    try {
      final response = await post(
        ApiConfig.refresh,
        data: {'refresh_token': refreshToken},
      );
      return AuthResponseDto.fromJson(response.data);
    } catch (e) {
      Logger.e('Refresh token failed', error: e);
      rethrow;
    }
  }

  @override
  Future<void> logout() async {
    try {
      await post(ApiConfig.logout);
      // Clear stored tokens
      await _secureStorage.delete(key: 'access_token');
      await _secureStorage.delete(key: 'refresh_token');
    } catch (e) {
      // Even if logout fails on server, clear local tokens
      await _secureStorage.delete(key: 'access_token');
      await _secureStorage.delete(key: 'refresh_token');
      Logger.e('Logout failed', error: e);
      rethrow;
    }
  }

  @override
  Future<List<ApplicationDto>> getApplications({
    Map<String, dynamic>? filters,
  }) async {
    try {
      final response = await get(
        ApiConfig.getApplications,
        queryParameters: filters,
      );
      final List<dynamic> data = response.data['applications'] ?? response.data;
      return data.map((json) => ApplicationDto.fromJson(json)).toList();
    } catch (e) {
      Logger.e('Get applications failed', error: e);
      rethrow;
    }
  }

  @override
  Future<ApplicationDto> getApplication(String id) async {
    try {
      final response = await get('${ApiConfig.applications}/$id');
      return ApplicationDto.fromJson(response.data);
    } catch (e) {
      Logger.e('Get application failed', error: e);
      rethrow;
    }
  }

  @override
  Future<ApplicationDto> createApplication(ApplicationDto application) async {
    try {
      final response = await post(
        ApiConfig.createApplication,
        data: application.toJson(),
      );
      return ApplicationDto.fromJson(response.data);
    } catch (e) {
      Logger.e('Create application failed', error: e);
      rethrow;
    }
  }

  @override
  Future<ApplicationDto> updateApplication(
    String id,
    ApplicationDto application,
  ) async {
    try {
      final response = await put(
        '${ApiConfig.updateApplication}/$id',
        data: application.toJson(),
      );
      return ApplicationDto.fromJson(response.data);
    } catch (e) {
      Logger.e('Update application failed', error: e);
      rethrow;
    }
  }

  @override
  Future<void> deleteApplication(String id) async {
    try {
      await delete('${ApiConfig.applications}/$id');
    } catch (e) {
      Logger.e('Delete application failed', error: e);
      rethrow;
    }
  }

  @override
  Future<String> uploadFile(
    File file,
    String applicationId,
    String fileType, {
    Map<String, String>? additionalData,
  }) async {
    try {
      final formData = FormData();

      formData.files.add(
        MapEntry(
          'file',
          await MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
          ),
        ),
      );

      formData.fields.add(MapEntry('application_id', applicationId));
      formData.fields.add(MapEntry('file_type', fileType));
      if (additionalData != null) {
        additionalData.forEach((key, value) {
          formData.fields.add(MapEntry(key, value));
        });
      }

      final response = await _dio.post(
        ApiConfig.uploadFile,
        data: formData,
        options: Options(headers: {'Content-Type': 'multipart/form-data'}),
      );

      return response.data['file_url'] ?? response.data['url'] ?? '';
    } catch (e) {
      Logger.e('Upload file failed', error: e);
      rethrow;
    }
  }

  @override
  Future<List<String>> uploadFiles(
    List<File> files,
    String applicationId,
    String fileType,
  ) async {
    try {
      final List<String> urls = [];
      for (final file in files) {
        final url = await uploadFile(file, applicationId, fileType);
        urls.add(url);
      }
      return urls;
    } catch (e) {
      Logger.e('Upload files failed', error: e);
      rethrow;
    }
  }

  @override
  Future<SyncStatusDto> getSyncStatus(String applicationId) async {
    try {
      final response = await get(
        '${ApiConfig.syncApplications}/$applicationId/status',
      );
      return SyncStatusDto.fromJson(response.data);
    } catch (e) {
      Logger.e('Get sync status failed', error: e);
      rethrow;
    }
  }

  @override
  Future<List<SyncStatusDto>> getBatchSyncStatus(
    List<String> applicationIds,
  ) async {
    try {
      final response = await post(
        '${ApiConfig.syncApplications}/batch-status',
        data: {'application_ids': applicationIds},
      );
      final List<dynamic> data = response.data['statuses'] ?? response.data;
      return data.map((json) => SyncStatusDto.fromJson(json)).toList();
    } catch (e) {
      Logger.e('Get batch sync status failed', error: e);
      rethrow;
    }
  }

  @override
  Future<void> markAsSynced(String applicationId) async {
    try {
      await post('${ApiConfig.syncApplications}/$applicationId/mark-synced');
    } catch (e) {
      Logger.e('Mark as synced failed', error: e);
      rethrow;
    }
  }

  @override
  Future<List<ApplicationDto>> createApplicationsBatch(
    List<ApplicationDto> applications,
  ) async {
    try {
      final response = await post(
        '${ApiConfig.applications}/batch',
        data: {
          'applications': applications.map((app) => app.toJson()).toList(),
        },
      );
      final List<dynamic> data = response.data['applications'] ?? response.data;
      return data.map((json) => ApplicationDto.fromJson(json)).toList();
    } catch (e) {
      Logger.e('Create applications batch failed', error: e);
      rethrow;
    }
  }

  @override
  Future<List<ApplicationDto>> updateApplicationsBatch(
    List<ApplicationDto> applications,
  ) async {
    try {
      final response = await put(
        '${ApiConfig.applications}/batch',
        data: {
          'applications': applications.map((app) => app.toJson()).toList(),
        },
      );
      final List<dynamic> data = response.data['applications'] ?? response.data;
      return data.map((json) => ApplicationDto.fromJson(json)).toList();
    } catch (e) {
      Logger.e('Update applications batch failed', error: e);
      rethrow;
    }
  }

  @override
  Future<bool> isServerHealthy() async {
    try {
      final response = await get('/health');
      return response.statusCode == 200;
    } catch (e) {
      Logger.e('Health check failed', error: e);
      return false;
    }
  }

  @override
  Future<void> updateApplicationStatus(
    String applicationId,
    String newStatus, {
    String? rejectionReason,
  }) async {
    try {
      final data = {'status': newStatus};
      if (rejectionReason != null) {
        data['rejection_reason'] = rejectionReason;
      }
      
      await put('${ApiConfig.applications}/$applicationId/status', data: data);
    } catch (e) {
      Logger.e('Update application status failed', error: e);
      rethrow;
    }
  }

  @override
  Future<String> uploadDocument(
    File file,
    String applicationId,
    String documentType, {
    Map<String, String>? additionalData,
  }) async {
    try {
      final formData = FormData();

      formData.files.add(
        MapEntry(
          'file',
          await MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
          ),
        ),
      );

      formData.fields.add(MapEntry('application_id', applicationId));
      formData.fields.add(MapEntry('document_type', documentType));
      if (additionalData != null) {
        additionalData.forEach((key, value) {
          formData.fields.add(MapEntry(key, value));
        });
      }

      final response = await _dio.post(
        ApiConfig.uploadFile,
        data: formData,
        options: Options(headers: {'Content-Type': 'multipart/form-data'}),
      );

      return response.data['file_url'] ?? response.data['url'] ?? '';
    } catch (e) {
      Logger.e('Upload document failed', error: e);
      rethrow;
    }
  }

  @override
  Future<Uint8List> downloadFile(String fileId) async {
    try {
      final response = await get(
        '${ApiConfig.getFile}/$fileId',
        options: Options(responseType: ResponseType.bytes),
      );
      
      return response.data as Uint8List;
    } catch (e) {
      Logger.e('Download file failed', error: e);
      rethrow;
    }
  }

  /// Convert Dio exceptions to API exceptions
  ApiException _handleError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ApiException(
          'Connection timeout. Please check your internet connection.',
          type: ApiExceptionType.timeout,
        );
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message =
            error.response?.data?['message'] ?? 'Unknown error occurred';

        if (statusCode == 401) {
          return ApiException(
            'Authentication failed. Please login again.',
            type: ApiExceptionType.unauthorized,
            statusCode: statusCode,
          );
        } else if (statusCode == 403) {
          return ApiException(
            'Access denied. You don\'t have permission to perform this action.',
            type: ApiExceptionType.forbidden,
            statusCode: statusCode,
          );
        } else if (statusCode == 404) {
          return ApiException(
            'Resource not found.',
            type: ApiExceptionType.notFound,
            statusCode: statusCode,
          );
        } else if (statusCode == 422) {
          return ApiException(
            'Validation error: $message',
            type: ApiExceptionType.validation,
            statusCode: statusCode,
            validationErrors: error.response?.data?['errors'],
          );
        } else if (statusCode != null && statusCode >= 500) {
          return ApiException(
            'Server error. Please try again later.',
            type: ApiExceptionType.server,
            statusCode: statusCode,
          );
        }

        return ApiException(
          message,
          type: ApiExceptionType.unknown,
          statusCode: statusCode,
        );
      case DioExceptionType.cancel:
        return ApiException(
          'Request was cancelled.',
          type: ApiExceptionType.cancelled,
        );
      case DioExceptionType.unknown:
      default:
        return ApiException(
          'Network error. Please check your internet connection.',
          type: ApiExceptionType.network,
        );
    }
  }
}

/// API Exception class for handling API errors
class ApiException implements Exception {
  final String message;
  final ApiExceptionType type;
  final int? statusCode;
  final Map<String, dynamic>? validationErrors;

  ApiException(
    this.message, {
    required this.type,
    this.statusCode,
    this.validationErrors,
  });

  @override
  String toString() => message;
}

/// API Exception types
enum ApiExceptionType {
  network,
  timeout,
  unauthorized,
  forbidden,
  notFound,
  validation,
  server,
  cancelled,
  unknown,
}
