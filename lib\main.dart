import 'package:flutter/material.dart';
import 'app_widget.dart';
import 'app_binding.dart';
import 'services/local_storage_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize local storage service
  await LocalStorageService().init();

  // Initialize app bindings (services, repositories, etc.)
  AppBinding().dependencies();

  runApp(const AppWidget());
}
