class ApiConfig {
  // Base URL - Configured for FastAPI backend
  static const String baseUrl = 'https://lc-le-production.up.railway.app/api/v1';

  // Endpoints
  static const String auth = '/auth';
  static const String users = '/users';
  static const String departments = '/departments';
  static const String branches = '/branches';
  static const String applications = '/applications';
  static const String files = '/files';

  // Auth endpoints (FastAPI compatible)
  static const String login = '$auth/token'; // FastAPI OAuth2 token endpoint
  static const String logout = '$auth/logout';
  static const String refresh = '$auth/refresh';
  static const String profile = '$auth/me'; // FastAPI user profile endpoint

  // Application endpoints
  static const String createApplication = applications;
  static const String updateApplication = applications;
  static const String getApplications = applications;
  static const String syncApplications = '$applications/sync';

  // File upload endpoints
  static const String uploadFile = '$files/upload';
  static const String downloadFile = '$files/download';
  static const String getFile = files;

  // Request timeouts (optimized for mobile)
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 60000; // 60 seconds for file uploads
  static const int sendTimeout = 60000; // 60 seconds for file uploads

  // Retry configuration
  static const int maxRetries = 3;
  static const int retryDelay = 1000; // 1 second
  static const double retryBackoffMultiplier = 2.0; // Exponential backoff

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // File upload configuration
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];

  // Sync configuration
  static const int syncBatchSize =
      10; // Number of applications to sync in one batch
  static const int syncRetryAttempts = 3;
  static const int syncRetryDelayMs = 2000; // 2 seconds

  // FastAPI specific headers
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': 'LC-WorkFlow-Mobile/1.0',
  };

  // Environment configuration
  static bool get isProduction => const bool.fromEnvironment('dart.vm.product');
  static bool get enableLogging => !isProduction;
}
