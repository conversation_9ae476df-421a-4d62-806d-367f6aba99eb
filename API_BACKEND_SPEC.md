# LC Work Flow - Backend API Specification

## Overview

This document outlines the backend API requirements for the LC Work Flow application. The backend will be built using FastAPI (Python) with PostgreSQL as the primary database and Redis for caching and session management.

## Technology Stack

- **Framework**: FastAPI (Python 3.9+)
- **Database**: PostgreSQL 14+
- **Cache/Session**: Redis 6+
- **Authentication**: JWT tokens
- **File Storage**: Local storage or AWS S3
- **API Documentation**: Swagger/OpenAPI (auto-generated by FastAPI)

## Database Schema

### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    phone_number VA<PERSON>HA<PERSON>(20),
    role VARCHAR(20) NOT NULL DEFAULT 'officer',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    department_id UUID REFERENCES departments(id),
    branch_id UUID REFERENCES branches(id),
    profile_image_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP
);

CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
```

### Departments Table
```sql
CREATE TABLE departments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    manager_id UUID REFERENCES users(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_departments_code ON departments(code);
CREATE INDEX idx_departments_is_active ON departments(is_active);
```

### Branches Table
```sql
CREATE TABLE branches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    address TEXT NOT NULL,
    phone_number VARCHAR(20),
    email VARCHAR(255),
    manager_id UUID REFERENCES users(id),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_branches_code ON branches(code);
CREATE INDEX idx_branches_is_active ON branches(is_active);
```

### Customer Applications Table
```sql
CREATE TABLE customer_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    
    -- Borrower Information
    id_card_type VARCHAR(50),
    id_number VARCHAR(50),
    full_name_khmer VARCHAR(255),
    full_name_latin VARCHAR(255),
    phone VARCHAR(20),
    date_of_birth DATE,
    portfolio_officer_name VARCHAR(255),
    
    -- Loan Details
    requested_amount DECIMAL(15, 2),
    loan_purposes JSONB,
    purpose_details TEXT,
    product_type VARCHAR(50),
    desired_loan_term VARCHAR(50),
    requested_disbursement_date DATE,
    
    -- Guarantor Information
    guarantor_name VARCHAR(255),
    guarantor_phone VARCHAR(20),
    
    -- File references
    borrower_nid_photo_id UUID REFERENCES files(id),
    borrower_home_photo_id UUID REFERENCES files(id),
    borrower_business_photo_id UUID REFERENCES files(id),
    guarantor_nid_photo_id UUID REFERENCES files(id),
    guarantor_home_photo_id UUID REFERENCES files(id),
    guarantor_business_photo_id UUID REFERENCES files(id),
    profile_photo_id UUID REFERENCES files(id),
    
    -- Additional data
    collaterals JSONB,
    documents JSONB,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    submitted_at TIMESTAMP,
    approved_at TIMESTAMP,
    approved_by UUID REFERENCES users(id),
    rejected_at TIMESTAMP,
    rejected_by UUID REFERENCES users(id),
    rejection_reason TEXT
);

CREATE INDEX idx_applications_user_id ON customer_applications(user_id);
CREATE INDEX idx_applications_status ON customer_applications(status);
CREATE INDEX idx_applications_created_at ON customer_applications(created_at);
CREATE INDEX idx_applications_id_number ON customer_applications(id_number);
```

### Files Table
```sql
CREATE TABLE files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    uploaded_by UUID NOT NULL REFERENCES users(id),
    application_id UUID REFERENCES customer_applications(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_files_application_id ON files(application_id);
CREATE INDEX idx_files_uploaded_by ON files(uploaded_by);
```

## API Endpoints

### Authentication Endpoints

#### POST /api/v1/auth/login
Login user and return JWT tokens.

**Request Body:**
```json
{
    "username": "string",
    "password": "string"
}
```

**Response:**
```json
{
    "access_token": "string",
    "refresh_token": "string",
    "token_type": "bearer",
    "expires_in": 3600,
    "user": {
        "id": "uuid",
        "username": "string",
        "email": "string",
        "firstName": "string",
        "lastName": "string",
        "role": "string",
        "status": "string",
        "departmentId": "uuid",
        "branchId": "uuid",
        "profileImageUrl": "string",
        "createdAt": "datetime",
        "updatedAt": "datetime",
        "lastLoginAt": "datetime"
    }
}
```

#### POST /api/v1/auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
    "refresh_token": "string"
}
```

**Response:**
```json
{
    "access_token": "string",
    "refresh_token": "string",
    "token_type": "bearer",
    "expires_in": 3600
}
```

#### POST /api/v1/auth/logout
Logout user and invalidate tokens.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
    "message": "Successfully logged out"
}
```

#### GET /api/v1/auth/profile
Get current user profile.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
    "id": "uuid",
    "username": "string",
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "phoneNumber": "string",
    "role": "string",
    "status": "string",
    "departmentId": "uuid",
    "branchId": "uuid",
    "profileImageUrl": "string",
    "createdAt": "datetime",
    "updatedAt": "datetime",
    "lastLoginAt": "datetime"
}
```

#### PUT /api/v1/auth/profile
Update user profile.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
    "firstName": "string",
    "lastName": "string",
    "phoneNumber": "string",
    "email": "string"
}
```

#### POST /api/v1/auth/change-password
Change user password.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
    "current_password": "string",
    "new_password": "string"
}
```

### Application Endpoints

#### GET /api/v1/applications
Get applications list with pagination and filtering.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page`: int (default: 1)
- `limit`: int (default: 20, max: 100)
- `status`: string (draft, pending, approved, rejected, disbursed, completed)
- `user_id`: uuid (filter by user)
- `search`: string (search in names, ID numbers)
- `date_from`: date
- `date_to`: date

**Response:**
```json
{
    "applications": [
        {
            "id": "uuid",
            "userId": "uuid",
            "status": "string",
            "idCardType": "string",
            "idNumber": "string",
            "fullNameKhmer": "string",
            "fullNameLatin": "string",
            "phone": "string",
            "dateOfBirth": "date",
            "portfolioOfficerName": "string",
            "requestedAmount": 0.0,
            "loanPurposes": ["string"],
            "purposeDetails": "string",
            "productType": "string",
            "desiredLoanTerm": "string",
            "requestedDisbursementDate": "date",
            "guarantorName": "string",
            "guarantorPhone": "string",
            "collaterals": {},
            "documents": {},
            "createdAt": "datetime",
            "updatedAt": "datetime",
            "submittedAt": "datetime",
            "approvedAt": "datetime",
            "approvedBy": "uuid",
            "rejectedAt": "datetime",
            "rejectedBy": "uuid",
            "rejectionReason": "string"
        }
    ],
    "pagination": {
        "page": 1,
        "limit": 20,
        "total": 100,
        "pages": 5
    }
}
```

#### POST /api/v1/applications
Create new application.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
    "idCardType": "string",
    "idNumber": "string",
    "fullNameKhmer": "string",
    "fullNameLatin": "string",
    "phone": "string",
    "dateOfBirth": "date",
    "portfolioOfficerName": "string",
    "requestedAmount": 0.0,
    "loanPurposes": ["string"],
    "purposeDetails": "string",
    "productType": "string",
    "desiredLoanTerm": "string",
    "requestedDisbursementDate": "date",
    "guarantorName": "string",
    "guarantorPhone": "string",
    "collaterals": {},
    "documents": {}
}
```

#### GET /api/v1/applications/{id}
Get specific application.

**Headers:** `Authorization: Bearer <token>`

#### PUT /api/v1/applications/{id}
Update application.

**Headers:** `Authorization: Bearer <token>`

#### DELETE /api/v1/applications/{id}
Delete application (soft delete).

**Headers:** `Authorization: Bearer <token>`

#### POST /api/v1/applications/{id}/submit
Submit application for review.

**Headers:** `Authorization: Bearer <token>`

#### POST /api/v1/applications/{id}/approve
Approve application (manager/admin only).

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
    "notes": "string"
}
```

#### POST /api/v1/applications/{id}/reject
Reject application (manager/admin only).

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
    "reason": "string"
}
```

### File Upload Endpoints

#### POST /api/v1/files/upload
Upload file.

**Headers:** `Authorization: Bearer <token>`

**Request:** `multipart/form-data`
- `file`: File
- `application_id`: uuid (optional)

**Response:**
```json
{
    "file_id": "uuid",
    "file_url": "string",
    "filename": "string",
    "file_size": 0,
    "mime_type": "string"
}
```

#### GET /api/v1/files/{id}
Download file.

**Headers:** `Authorization: Bearer <token>`

#### DELETE /api/v1/files/{id}
Delete file.

**Headers:** `Authorization: Bearer <token>`

### User Management Endpoints (Admin/Manager only)

#### GET /api/v1/users
Get users list.

#### POST /api/v1/users
Create new user.

#### GET /api/v1/users/{id}
Get specific user.

#### PUT /api/v1/users/{id}
Update user.

#### DELETE /api/v1/users/{id}
Deactivate user.

### Department Endpoints

#### GET /api/v1/departments
Get departments list.

#### POST /api/v1/departments
Create department (admin only).

#### PUT /api/v1/departments/{id}
Update department (admin only).

### Branch Endpoints

#### GET /api/v1/branches
Get branches list.

#### POST /api/v1/branches
Create branch (admin only).

#### PUT /api/v1/branches/{id}
Update branch (admin only).

## Error Responses

All endpoints return consistent error responses:

```json
{
    "error": {
        "code": "string",
        "message": "string",
        "details": {}
    }
}
```

Common HTTP status codes:
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `500`: Internal Server Error

## Authentication & Authorization

- JWT tokens with 1-hour expiration for access tokens
- Refresh tokens with 30-day expiration
- Role-based access control:
  - `admin`: Full access
  - `manager`: Can approve/reject applications, manage users in their branch
  - `officer`: Can create/edit applications
  - `viewer`: Read-only access

## File Storage

- Files stored locally in `/uploads` directory or AWS S3
- File access controlled by user permissions
- Maximum file size: 10MB per file
- Supported formats: JPG, PNG, PDF

## Rate Limiting

- 100 requests per minute per user for general endpoints
- 10 requests per minute for file uploads
- 5 requests per minute for authentication endpoints

## Deployment Requirements

### Environment Variables
```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/lc_workflow
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=60
JWT_REFRESH_TOKEN_EXPIRE_DAYS=30

# File Storage
UPLOAD_DIR=/uploads
MAX_FILE_SIZE=10485760  # 10MB

# API
API_V1_STR=/api/v1
PROJECT_NAME=LC Work Flow API
DEBUG=false
```

### Docker Compose Example
```yaml
version: '3.8'
services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/lc_workflow
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    volumes:
      - ./uploads:/uploads

  db:
    image: postgres:14
    environment:
      - POSTGRES_DB=lc_workflow
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

## Testing

- Unit tests for all business logic
- Integration tests for API endpoints
- Load testing for performance validation
- Security testing for authentication and authorization

## Monitoring & Logging

- Structured logging with correlation IDs
- Application metrics (response times, error rates)
- Database query monitoring
- File upload/download tracking
- User activity logging for audit purposes