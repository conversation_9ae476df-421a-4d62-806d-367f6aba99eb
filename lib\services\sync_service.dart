import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:lc_work_flow/core/utils/logger.dart';
import 'package:lc_work_flow/models/customer_model.dart';
import 'database_helper.dart';

enum SyncStatus { synced, pending, failed }

class SyncService {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final String _baseUrl = 'https://api.example.com'; // Placeholder API endpoint

  // Saves a new customer application to local storage and marks it for sync.
  Future<void> saveCustomerApplication(Customer customer) async {
    try {
      final customerMap = customer.toJson();
      customerMap['sync_status'] =
          SyncStatus.pending.toString(); // Mark as pending
      customerMap['last_modified_locally'] =
          DateTime.now().toIso8601String(); // Add timestamp
      await _dbHelper.insertCustomerApplication(customerMap);
    } catch (e) {
      Logger.e('Error saving customer application locally: $e');
    }
  }

  // Updates an existing customer application in local storage and marks it for sync.
  Future<void> updateCustomerApplication(Customer customer) async {
    try {
      final customerMap = customer.toJson();
      customerMap['sync_status'] =
          SyncStatus.pending.toString(); // Mark as pending
      customerMap['last_modified_locally'] =
          DateTime.now().toIso8601String(); // Add timestamp
      await _dbHelper.updateCustomerApplication(customer.id, customerMap);
    } catch (e) {
      Logger.e('Error updating customer application locally: $e');
    }
  }

  // Fetches records from the local database that need to be pushed to the server.
  Future<List<Map<String, dynamic>>> _getUnsyncedRecords() async {
    final db = await _dbHelper.database;
    return await db.query(
      'customer_applications',
      where: "sync_status != '${SyncStatus.synced.toString()}'",
    );
  }

  // Pushes local changes (creations, updates) to the remote server.
  Future<void> pushLocalChanges() async {
    final unsyncedRecords = await _getUnsyncedRecords();
    if (unsyncedRecords.isEmpty) {
      return;
    }

    final creations =
        unsyncedRecords
            .where((r) => r['sync_status'] == SyncStatus.pending.toString())
            .toList();
    final updates =
        unsyncedRecords
            .where((r) => r['sync_status'] == SyncStatus.pending.toString())
            .toList(); // Assuming updates are also 'pending'

    try {
      // Placeholder for pushing created records
      if (creations.isNotEmpty) {
        final response = await http.post(
          Uri.parse('$_baseUrl/api/sync/push'),
          headers: {'Content-Type': 'application/json'},
          body: json.encode({'created': creations}),
        );

        if (response.statusCode == 200) {
          final responseData = json.decode(response.body);
          // Update local records with server_id and set sync_status to 'synced'
          for (var record in responseData['synced_records']) {
            await _dbHelper.updateCustomerApplication(record['local_id'], {
              'server_id': record['server_id'],
              'sync_status': SyncStatus.synced.toString(),
            });
          }
        } else {
          // Mark as failed if server response is not 200
          for (var record in creations) {
            await _dbHelper.updateCustomerApplication(record['id'], {
              'sync_status': SyncStatus.failed.toString(),
            });
          }
        }
      }

      // Placeholder for pushing updated records
      if (updates.isNotEmpty) {
        final response = await http.put(
          Uri.parse('$_baseUrl/api/sync/push'),
          headers: {'Content-Type': 'application/json'},
          body: json.encode({'updated': updates}),
        );

        if (response.statusCode == 200) {
          // Update sync_status to 'synced' for successfully updated records
          for (var record in updates) {
            await _dbHelper.updateCustomerApplication(record['id'], {
              'sync_status': SyncStatus.synced.toString(),
            });
          }
        } else {
          // Mark as failed if server response is not 200
          for (var record in updates) {
            await _dbHelper.updateCustomerApplication(record['id'], {
              'sync_status': SyncStatus.failed.toString(),
            });
          }
        }
      }
    } catch (e) {
      // Handle network errors or other exceptions
      Logger.e('Error pushing local changes: $e');
      // Mark all pending records as failed if an error occurs during push
      for (var record in unsyncedRecords) {
        await _dbHelper.updateCustomerApplication(record['id'], {
          'sync_status': SyncStatus.failed.toString(),
        });
      }
    }
  }

  // Fetches remote changes and updates the local database.
  Future<void> pullRemoteChanges() async {
    try {
      // Placeholder for fetching remote updates
      final response = await http.get(
        Uri.parse('$_baseUrl/api/sync/pull?last_sync_timestamp=...'),
      );

      if (response.statusCode == 200) {
        final remoteRecords = json.decode(response.body) as List;

        for (var remoteRecord in remoteRecords) {
          final localRecord = await _dbHelper.getCustomerApplication(
            remoteRecord['id'],
          );

          if (localRecord == null) {
            // New record from server, insert locally
            await _dbHelper.insertCustomerApplication(remoteRecord);
          } else {
            // Record exists, check for conflicts
            final remoteTimestamp = DateTime.parse(remoteRecord['updated_at']);
            final localTimestamp = DateTime.parse(
              localRecord['last_modified_locally'],
            );

            // "Last write wins" conflict resolution
            if (remoteTimestamp.isAfter(localTimestamp)) {
              await _dbHelper.updateCustomerApplication(
                localRecord['id'],
                remoteRecord,
              );
            }
          }
        }
      }
    } catch (e) {
      // Handle network errors or other exceptions
      Logger.e('Error pulling remote changes: $e');
    }
  }

  // Main synchronization method to be called periodically or on-demand.
  Future<void> sync() async {
    await pushLocalChanges();
    await pullRemoteChanges();
  }
}
