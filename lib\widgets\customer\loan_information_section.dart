import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../models/customer_model.dart';

class LoanInformationSection extends StatelessWidget {
  final Customer customer;

  const LoanInformationSection({super.key, required this.customer});

  @override
  Widget build(BuildContext context) {
    final currencyFormatRiel = NumberFormat.currency(
      locale: 'km',
      symbol: '៛',
      decimalDigits: 2, // Changed to 2 for 1,000,000.00៛ format
      // Grouping and decimal separators are handled by the locale if not specified
      // For Khmer locale, this often means no grouping separator by default.
      // We might need to explicitly set them if the locale doesn't provide the desired format.
      // For 1,000,000.00៛, we need to ensure grouping separator is ',' and decimal is '.'
      // This is usually done by setting the locale correctly or providing custom patterns.
      // Given the example, it seems a specific pattern is needed.
      // Let's assume for now that decimalDigits: 2 combined with 'km' locale will provide the desired format.
      // If not, we'll revisit with a custom pattern.
    );


    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Loan Information',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF12306E),
          ),
        ),
        const SizedBox(height: 12),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                _buildDetailRow(
                  context,
                  'Loan Amount',
                  currencyFormatRiel.format(customer.loanAmount),
                  Icons.money,
                  Colors.green,
                ),
                const Divider(height: 24),
                _buildDetailRow(
                  context,
                  'Interest Rate',
                  '${customer.interestRate ?? 0.0}%',
                  Icons.percent,
                  Colors.orange,
                ),
                const Divider(height: 24),
                _buildDetailRow(
                  context,
                  'Start Date',
                  customer.formattedStartDate,
                  Icons.calendar_today,
                  Colors.blue,
                ),
                if (customer.loanEndDate != null) ...[
                  const Divider(height: 24),
                  _buildDetailRow(
                    context,
                    'End Date',
                    DateFormat('MMM d, y').format(customer.loanEndDate!),
                    Icons.calendar_today_outlined,
                    Colors.purple,
                  ),
                ],
                if (customer.loanPurpose != null) ...[
                  const Divider(height: 24),
                  _buildDetailRow(
                    context,
                    'Purpose',
                    customer.loanPurpose!,
                    Icons.description,
                    Colors.teal,
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color iconColor,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: iconColor.withValues(
              alpha: (255 * 0.1).roundToDouble(),
              red: iconColor.r.toDouble(),
              green: iconColor.g.toDouble(),
              blue: iconColor.b.toDouble(),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 20, color: iconColor),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: Theme.of(
                  context,
                ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
