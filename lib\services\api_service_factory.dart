import 'package:lc_work_flow/services/api_service.dart';
import 'package:lc_work_flow/services/interfaces/api_service_interface.dart';

/// Factory class to provide API service instances
class ApiServiceFactory {
  static IApiService? _instance;

  /// Get the singleton instance of the API service
  static IApiService getInstance() {
    _instance ??= ApiService();
    return _instance!;
  }

  /// Initialize the API service
  static void initialize() {
    final apiService = getInstance() as ApiService;
    apiService.initialize();
  }

  /// Reset the instance (useful for testing)
  static void reset() {
    _instance = null;
  }
}
