import 'package:get/get.dart';
import 'package:lc_work_flow/screen/dashboard/controllers/dashboard_controller.dart';
import 'package:lc_work_flow/screen/login/controllers/login_controller.dart';
import 'package:lc_work_flow/screen/splash/controllers/splash_controller.dart';
import 'package:lc_work_flow/services/api_service.dart';
import 'package:lc_work_flow/services/auth_service.dart';
import 'package:lc_work_flow/services/simple_connectivity_service.dart';
import 'package:lc_work_flow/services/sync_service.dart';

class AppBinding implements Bindings {
  @override
  void dependencies() {
    // Core services - Initialize first
    Get.put<SimpleConnectivityService>(
      SimpleConnectivityService(),
      permanent: true,
    );

    // API and Auth services
    Get.put<ApiService>(ApiService(), permanent: true);
    Get.put<AuthService>(AuthService(), permanent: true);
    Get.put<SyncService>(SyncService(), permanent: true);

    // Controllers
    Get.put(LoginController());
    Get.put(SplashController());
    Get.put(DashboardController());
  }
}
