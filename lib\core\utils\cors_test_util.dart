import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:lc_work_flow/core/config/api_config.dart';
import 'package:lc_work_flow/core/utils/logger.dart';

/// Utility class for testing CORS configuration with the backend API
class CorsTestUtil {
  /// Tests the CORS configuration by making a request to the CORS test endpoint
  ///
  /// Returns a map with the test results or an error message
  static Future<Map<String, dynamic>> testCorsConfiguration() async {
    final dio = Dio(
      BaseOptions(
        baseUrl:
            ApiConfig.baseUrl
                .split('/api/v1')
                .first, // Use base URL without API version
        connectTimeout: Duration(milliseconds: ApiConfig.connectTimeout),
        receiveTimeout: Duration(milliseconds: ApiConfig.receiveTimeout),
        headers: {
          'X-Flutter-Mode': kReleaseMode ? 'release' : 'debug',
          'X-Platform': _getPlatformName(),
          'X-LC-App-Version': '1.0.0',
          'X-LC-Device-ID': 'test-device',
          'Origin': _getOriginHeader(),
        },
      ),
    );

    try {
      // Add logging interceptor in debug mode
      if (!kReleaseMode) {
        dio.interceptors.add(
          LogInterceptor(
            requestBody: true,
            responseBody: true,
            logPrint: (obj) => Logger.d('CORS Test: $obj'),
          ),
        );
      }

      // Make request to CORS test endpoint
      final response = await dio.get('/cors-test');

      if (response.statusCode == 200) {
        Logger.i('CORS test successful: ${response.data}');
        return {
          'success': true,
          'message': 'CORS configuration is working correctly',
          'data': response.data,
        };
      } else {
        Logger.e('CORS test failed with status code: ${response.statusCode}');
        return {
          'success': false,
          'message':
              'CORS test failed with status code: ${response.statusCode}',
          'error': response.statusMessage,
        };
      }
    } catch (e) {
      Logger.e('CORS test error: $e');
      return {
        'success': false,
        'message': 'CORS test failed with error',
        'error': e.toString(),
      };
    }
  }

  /// Gets the platform name for the X-Platform header
  static String _getPlatformName() {
    if (kIsWeb) return 'web';

    // For mobile/desktop platforms, we would use Platform.isIOS, etc.
    // But for simplicity in this utility, we'll just return 'flutter'
    return 'flutter';
  }

  /// Gets the origin header based on the current platform and environment
  static String _getOriginHeader() {
    if (kIsWeb) {
      // In web, we would use window.location.origin, but for this utility
      // we'll just use a placeholder
      return 'http://localhost:8080';
    }

    // For mobile apps, use a standard origin
    return 'app://lc_work_flow';
  }
}
