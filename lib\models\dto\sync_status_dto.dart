import 'package:equatable/equatable.dart';

/// Data Transfer Object for Sync Status API communication
class SyncStatusDto extends Equatable {
  final String applicationId;
  final String status; // 'synced', 'pending', 'failed', 'conflict'
  final String? lastSyncedAt;
  final String? errorMessage;
  final int? version;
  final Map<String, dynamic>? conflictData;

  const SyncStatusDto({
    required this.applicationId,
    required this.status,
    this.lastSyncedAt,
    this.errorMessage,
    this.version,
    this.conflictData,
  });

  Map<String, dynamic> toJson() {
    return {
      'application_id': applicationId,
      'status': status,
      'last_synced_at': lastSyncedAt,
      'error_message': errorMessage,
      'version': version,
      'conflict_data': conflictData,
    };
  }

  factory SyncStatusDto.fromJson(Map<String, dynamic> json) {
    return SyncStatusDto(
      applicationId: json['application_id'] ?? '',
      status: json['status'] ?? 'pending',
      lastSyncedAt: json['last_synced_at'],
      errorMessage: json['error_message'],
      version: json['version'],
      conflictData: json['conflict_data'],
    );
  }

  SyncStatusDto copyWith({
    String? applicationId,
    String? status,
    String? lastSyncedAt,
    String? errorMessage,
    int? version,
    Map<String, dynamic>? conflictData,
  }) {
    return SyncStatusDto(
      applicationId: applicationId ?? this.applicationId,
      status: status ?? this.status,
      lastSyncedAt: lastSyncedAt ?? this.lastSyncedAt,
      errorMessage: errorMessage ?? this.errorMessage,
      version: version ?? this.version,
      conflictData: conflictData ?? this.conflictData,
    );
  }

  @override
  List<Object?> get props => [
    applicationId,
    status,
    lastSyncedAt,
    errorMessage,
    version,
    conflictData,
  ];
}
