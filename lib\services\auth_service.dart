import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:lc_work_flow/core/config/api_config.dart';
import 'package:lc_work_flow/core/utils/logger.dart';
import 'package:lc_work_flow/models/user_model.dart';
import 'package:lc_work_flow/services/api_service.dart';
import 'package:lc_work_flow/services/mock_api_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final ApiService _apiService = ApiService();

  User? _currentUser;
  User? get currentUser => _currentUser;

  Future<AuthResult> login(String username, String password) async {
    try {
      Map<String, dynamic> data;

      // Use mock API for development/testing
      if (MockApiService.useMockApi) {
        data = await MockApiService().login(username, password);
      } else {
        final response = await _apiService.post(
          ApiConfig.login,
          data: {'username': username, 'password': password},
        );

        if (response.statusCode != 200) {
          return AuthResult.failure('Login failed');
        }
        data = response.data;
      }

      // Store tokens
      await _secureStorage.write(
        key: 'access_token',
        value: data['access_token'],
      );
      await _secureStorage.write(
        key: 'refresh_token',
        value: data['refresh_token'],
      );

      // Store user data
      _currentUser = User.fromJson(data['user']);
      await _secureStorage.write(
        key: 'user_data',
        value: jsonEncode(_currentUser!.toJson()),
      );
      await _secureStorage.write(key: 'current_username', value: username);

      return AuthResult.success(_currentUser!);
    } catch (e) {
      return AuthResult.failure('Login failed: ${e.toString()}');
    }
  }

  Future<bool> logout() async {
    try {
      // Call logout endpoint to invalidate token on server
      if (MockApiService.useMockApi) {
        await MockApiService().logout();
      } else {
        await _apiService.post(ApiConfig.logout);
      }
    } catch (e) {
      // Continue with local logout even if server call fails
      Logger.e('Server logout failed: $e');
    }

    // Clear local storage
    await _secureStorage.delete(key: 'access_token');
    await _secureStorage.delete(key: 'refresh_token');
    await _secureStorage.delete(key: 'user_data');
    await _secureStorage.delete(key: 'current_username');
    _currentUser = null;

    return true;
  }

  Future<bool> isLoggedIn() async {
    final token = await _secureStorage.read(key: 'access_token');
    if (token == null) return false;

    // For mock API, just check if token exists and is recent
    if (MockApiService.useMockApi) {
      if (token.startsWith('mock_access_token_')) {
        // Load user data if not already loaded
        if (_currentUser == null) {
          await _loadUserData();
        }
        return true;
      }
      return false;
    }

    // Check if token is expired (for real JWT tokens)
    try {
      if (JwtDecoder.isExpired(token)) {
        // Try to refresh token
        return await _refreshToken();
      }
    } catch (e) {
      // If JWT decoding fails, assume token is invalid
      return false;
    }

    // Load user data if not already loaded
    if (_currentUser == null) {
      await _loadUserData();
    }

    return true;
  }

  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _secureStorage.read(key: 'refresh_token');
      if (refreshToken == null) return false;

      final response = await _apiService.post(
        ApiConfig.refresh,
        data: {'refresh_token': refreshToken},
      );

      if (response.statusCode == 200) {
        final data = response.data;
        await _secureStorage.write(
          key: 'access_token',
          value: data['access_token'],
        );

        if (data['refresh_token'] != null) {
          await _secureStorage.write(
            key: 'refresh_token',
            value: data['refresh_token'],
          );
        }

        return true;
      }
    } catch (e) {
      Logger.e('Token refresh failed: $e');
    }

    return false;
  }

  Future<void> _loadUserData() async {
    try {
      final userData = await _secureStorage.read(key: 'user_data');
      if (userData != null) {
        _currentUser = User.fromJson(jsonDecode(userData));
      } else {
        // Fetch user data from server
        await fetchUserProfile();
      }
    } catch (e) {
      Logger.e('Failed to load user data: $e');
    }
  }

  Future<User?> fetchUserProfile() async {
    try {
      final response = await _apiService.get(ApiConfig.profile);

      if (response.statusCode == 200) {
        _currentUser = User.fromJson(response.data);
        await _secureStorage.write(
          key: 'user_data',
          value: jsonEncode(_currentUser!.toJson()),
        );
        return _currentUser;
      }
    } catch (e) {
      Logger.e('Failed to fetch user profile: $e');
    }
    return null;
  }

  Future<String?> getAccessToken() async {
    return await _secureStorage.read(key: 'access_token');
  }

  Future<AuthResult> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final response = await _apiService.post(
        '${ApiConfig.auth}/change-password',
        data: {
          'current_password': currentPassword,
          'new_password': newPassword,
        },
      );

      if (response.statusCode == 200) {
        return AuthResult.success(_currentUser!);
      }
    } on ApiException catch (e) {
      return AuthResult.failure(e.message);
    } catch (e) {
      return AuthResult.failure('Password change failed: ${e.toString()}');
    }

    return AuthResult.failure('Password change failed');
  }

  Future<AuthResult> updateProfile({
    required String firstName,
    required String lastName,
    String? phoneNumber,
    String? email,
  }) async {
    try {
      final response = await _apiService.put(
        ApiConfig.profile,
        data: {
          'firstName': firstName,
          'lastName': lastName,
          'phoneNumber': phoneNumber,
          'email': email,
        },
      );

      if (response.statusCode == 200) {
        _currentUser = User.fromJson(response.data);
        await _secureStorage.write(
          key: 'user_data',
          value: jsonEncode(_currentUser!.toJson()),
        );
        return AuthResult.success(_currentUser!);
      }
    } on ApiException catch (e) {
      return AuthResult.failure(e.message);
    } catch (e) {
      return AuthResult.failure('Profile update failed: ${e.toString()}');
    }

    return AuthResult.failure('Profile update failed');
  }
}

class AuthResult {
  final bool isSuccess;
  final User? user;
  final String? error;

  AuthResult._({required this.isSuccess, this.user, this.error});

  factory AuthResult.success(User user) =>
      AuthResult._(isSuccess: true, user: user);

  factory AuthResult.failure(String error) =>
      AuthResult._(isSuccess: false, error: error);
}
