import 'package:dio/dio.dart';
import 'package:lc_work_flow/services/api_service.dart';

/// Utility class for API-related helper functions
class ApiUtils {
  /// Check if an error is a network connectivity issue
  static bool isNetworkError(dynamic error) {
    if (error is ApiException) {
      return error.type == ApiExceptionType.network ||
          error.type == ApiExceptionType.timeout;
    }
    if (error is DioException) {
      return error.type == DioExceptionType.connectionTimeout ||
          error.type == DioExceptionType.sendTimeout ||
          error.type == DioExceptionType.receiveTimeout ||
          (error.type == DioExceptionType.unknown &&
              error.error.toString().contains('SocketException'));
    }
    return false;
  }

  /// Check if an error requires authentication
  static bool isAuthError(dynamic error) {
    if (error is ApiException) {
      return error.type == ApiExceptionType.unauthorized;
    }
    if (error is DioException) {
      return error.response?.statusCode == 401;
    }
    return false;
  }

  /// Check if an error is a validation error
  static bool isValidationError(dynamic error) {
    if (error is ApiException) {
      return error.type == ApiExceptionType.validation;
    }
    if (error is DioException) {
      return error.response?.statusCode == 422;
    }
    return false;
  }

  /// Extract validation errors from API response
  static Map<String, List<String>>? getValidationErrors(dynamic error) {
    if (error is ApiException && error.validationErrors != null) {
      return Map<String, List<String>>.from(error.validationErrors!);
    }
    if (error is DioException && error.response?.data?['errors'] != null) {
      return Map<String, List<String>>.from(error.response!.data['errors']);
    }
    return null;
  }

  /// Get user-friendly error message
  static String getErrorMessage(dynamic error) {
    if (error is ApiException) {
      return error.message;
    }
    if (error is DioException) {
      if (error.response?.data?['message'] != null) {
        return error.response!.data['message'];
      }
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return 'Connection timeout. Please check your internet connection.';
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          if (statusCode == 401) {
            return 'Authentication failed. Please login again.';
          } else if (statusCode == 403) {
            return 'Access denied. You don\'t have permission to perform this action.';
          } else if (statusCode == 404) {
            return 'Resource not found.';
          } else if (statusCode != null && statusCode >= 500) {
            return 'Server error. Please try again later.';
          }
          return 'An error occurred. Please try again.';
        case DioExceptionType.cancel:
          return 'Request was cancelled.';
        case DioExceptionType.unknown:
        default:
          return 'Network error. Please check your internet connection.';
      }
    }
    return error.toString();
  }

  /// Format file size for display
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Check if file type is allowed for upload
  static bool isAllowedFileType(String fileName, List<String> allowedTypes) {
    final extension = fileName.split('.').last.toLowerCase();
    return allowedTypes.contains(extension);
  }

  /// Generate unique request ID for tracking
  static String generateRequestId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// Build query parameters for pagination
  static Map<String, dynamic> buildPaginationParams({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortOrder = 'asc',
  }) {
    final params = <String, dynamic>{'page': page, 'limit': limit};

    if (sortBy != null) {
      params['sort_by'] = sortBy;
      params['sort_order'] = sortOrder;
    }

    return params;
  }

  /// Build query parameters for filtering applications
  static Map<String, dynamic> buildApplicationFilters({
    String? status,
    String? portfolioOfficer,
    DateTime? fromDate,
    DateTime? toDate,
    double? minAmount,
    double? maxAmount,
  }) {
    final filters = <String, dynamic>{};

    if (status != null) filters['status'] = status;
    if (portfolioOfficer != null) {
      filters['portfolio_officer'] = portfolioOfficer;
    }
    if (fromDate != null) filters['from_date'] = fromDate.toIso8601String();
    if (toDate != null) filters['to_date'] = toDate.toIso8601String();
    if (minAmount != null) filters['min_amount'] = minAmount;
    if (maxAmount != null) filters['max_amount'] = maxAmount;

    return filters;
  }
}
