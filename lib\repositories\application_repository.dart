import 'dart:io';
import 'dart:typed_data';
import 'package:lc_work_flow/core/config/api_config.dart';
import 'package:lc_work_flow/core/utils/logger.dart';
import 'package:lc_work_flow/models/customer_model.dart';
import 'package:lc_work_flow/models/product_type.dart' as product_type;
import 'package:lc_work_flow/services/api_service.dart';
import 'package:lc_work_flow/services/local_storage_service.dart';
import 'package:lc_work_flow/services/connectivity_service.dart';

enum SyncStatus { synced, pending, failed, created, updated, deleted }

class ApplicationRepository {
  static final ApplicationRepository _instance =
      ApplicationRepository._internal();
  factory ApplicationRepository() => _instance;
  ApplicationRepository._internal();

  final ApiService _apiService = ApiService();
  final LocalStorageService _localStorageService = LocalStorageService();
  final ConnectivityService _connectivityService = ConnectivityService();

  // Create new application
  Future<ApplicationResult> createApplication(Customer application) async {
    try {
      // Always save locally first
      final localId = await _saveApplicationLocally(
        application,
        SyncStatus.created,
      );

      if (_connectivityService.isConnected) {
        // Try to sync immediately if online
        final syncResult = await _syncApplicationToServer(localId);
        if (syncResult.isSuccess) {
          return ApplicationResult.success(
            application.copyWith(
              id: localId,
              serverId: syncResult.serverId,
              syncStatus: SyncStatus.synced.name,
            ),
          );
        }
      }

      return ApplicationResult.success(
        application.copyWith(id: localId, syncStatus: SyncStatus.pending.name),
      );
    } catch (e) {
      return ApplicationResult.failure(
        'Failed to create application: ${e.toString()}',
      );
    }
  }

  // Update existing application
  Future<ApplicationResult> updateApplication(Customer application) async {
    try {
      // Update locally first
      await _updateApplicationLocally(application, SyncStatus.updated);

      if (_connectivityService.isConnected && application.serverId != null) {
        // Try to sync immediately if online and has server ID
        final syncResult = await _syncApplicationToServer(application.id);
        if (syncResult.isSuccess) {
          return ApplicationResult.success(
            application.copyWith(syncStatus: SyncStatus.synced.name),
          );
        }
      }

      return ApplicationResult.success(
        application.copyWith(syncStatus: SyncStatus.pending.name),
      );
    } catch (e) {
      return ApplicationResult.failure(
        'Failed to update application: ${e.toString()}',
      );
    }
  }

  // Get applications (local first, then sync if needed)
  Future<List<Customer>> getApplications({
    bool forceRefresh = false,
    int page = 1,
    int limit = ApiConfig.defaultPageSize,
  }) async {
    try {
      // Get local applications
      final localApplications = await _getLocalApplications();

      if (_connectivityService.isConnected && forceRefresh) {
        // Sync with server if online and refresh requested
        await syncApplications();
        return await _getLocalApplications();
      }

      return localApplications;
    } catch (e) {
      Logger.e('Error getting applications: $e');
      return await _getLocalApplications(); // Fallback to local data
    }
  }

  // Get single application
  Future<Customer?> getApplication(String id) async {
    try {
      final localApp = await _getLocalApplication(id);

      if (localApp != null && _connectivityService.isConnected) {
        // Try to get latest from server if available
        if (localApp.serverId != null) {
          final serverApp = await _getApplicationFromServer(localApp.serverId!);
          if (serverApp != null) {
            await _updateApplicationLocally(serverApp, SyncStatus.synced);
            return serverApp;
          }
        }
      }

      return localApp;
    } catch (e) {
      Logger.e('Error getting application: $e');
      return await _getLocalApplication(id);
    }
  }

  // Sync all pending applications
  Future<SyncResult> syncApplications() async {
    if (!_connectivityService.isConnected) {
      return SyncResult.failure('No internet connection');
    }

    try {
      int successCount = 0;
      int failureCount = 0;
      final errors = <String>[];

      // Get all pending applications
      final pendingApps = await _getPendingApplications();

      for (final app in pendingApps) {
        final result = await _syncApplicationToServer(app.id);
        if (result.isSuccess) {
          successCount++;
        } else {
          failureCount++;
          errors.add('${app.displayName}: ${result.error}');
        }
      }

      // Also pull latest from server
      await _pullApplicationsFromServer();

      return SyncResult.success(
        successCount: successCount,
        failureCount: failureCount,
        errors: errors,
      );
    } catch (e) {
      return SyncResult.failure('Sync failed: ${e.toString()}');
    }
  }

  // Upload file with document type
  Future<FileUploadResult> uploadFile(
    File file,
    String applicationId,
    String documentType,
  ) async {
    if (!_connectivityService.isConnected) {
      return FileUploadResult.failure('No internet connection');
    }

    try {
      final fileUrl = await _apiService.uploadDocument(
        file,
        applicationId,
        documentType,
      );

      if (fileUrl.isNotEmpty) {
        return FileUploadResult.success(
          fileId: fileUrl.hashCode.toString(), // Generate file ID from URL hash
          fileUrl: fileUrl,
        );
      }
    } on ApiException catch (e) {
      return FileUploadResult.failure(e.message);
    } catch (e) {
      return FileUploadResult.failure('File upload failed: ${e.toString()}');
    }

    return FileUploadResult.failure('File upload failed');
  }

  // Update application status
  Future<ApplicationResult> updateApplicationStatus(
    String applicationId,
    String newStatus,
    {String? rejectionReason}
  ) async {
    if (!_connectivityService.isConnected) {
      return ApplicationResult.failure('No internet connection');
    }

    try {
      await _apiService.updateApplicationStatus(
        applicationId,
        newStatus,
        rejectionReason: rejectionReason,
      );
      
      return ApplicationResult.success(null);
    } catch (e) {
      return ApplicationResult.failure(
        'Failed to update application status: ${e.toString()}',
      );
    }
  }

  // Download file
  Future<FileDownloadResult> downloadFile(String fileId) async {
    if (!_connectivityService.isConnected) {
      return FileDownloadResult.failure('No internet connection');
    }

    try {
      final fileData = await _apiService.downloadFile(fileId);
      return FileDownloadResult.success(fileData);
    } catch (e) {
      return FileDownloadResult.failure(
        'File download failed: ${e.toString()}',
      );
    }
  }

  // Private methods

  Future<String> _saveApplicationLocally(
    Customer application,
    SyncStatus status,
  ) async {
    final applicationId = DateTime.now().millisecondsSinceEpoch.toString();

    await _localStorageService.saveDraftApplication(
      idCardType: application.idCardType?.toString(),
      idNumber: application.idNumber,
      fullNameKhmer: application.fullNameKhmer,
      fullNameLatin: application.fullNameLatin,
      phone: application.phone,
      dateOfBirth: application.dateOfBirth,
      portfolioOfficerName: application.portfolioOfficerName,
      requestedAmount: application.requestedAmount,
      loanPurposes: application.loanPurposes?.map((e) => e.toString()).toList(),
      purposeDetails: application.purposeDetails,
      productType: application.productType?.toString(),
      desiredLoanTerm: application.desiredLoanTerm,
      requestedDisbursementDate: application.requestedDisbursementDate,
      guarantorName: application.guarantorName,
      guarantorPhone: application.guarantorPhone,
      idCardImages: [], // Handle file paths
      borrowerNidPhoto: application.borrowerNidPhotoPath,
      borrowerHomePhoto: application.borrowerHomeOrLandPhotoPath,
      borrowerBusinessPhoto: application.borrowerBusinessPhotoPath,
      guarantorNidPhoto: application.guarantorNidPhotoPath,
      guarantorHomePhoto: application.guarantorHomeOrLandPhotoPath,
      guarantorBusinessPhoto: application.guarantorBusinessPhotoPath,
      profilePhoto: application.profilePhotoPath,
      selectedCollateralTypes: [], // Handle collateral types
      collaterals: application.collaterals,
      documents: application.documents,
      existingId: applicationId,
    );

    return applicationId;
  }

  Future<void> _updateApplicationLocally(
    Customer application,
    SyncStatus status,
  ) async {
    // Implementation for updating local application
    // This would update the local database with new sync status
  }

  Future<List<Customer>> _getLocalApplications() async {
    final draftApps = await _localStorageService.getDraftApplications();
    final submittedApps = await _localStorageService.getSubmittedApplications();

    final allApps = [...draftApps, ...submittedApps];
    return allApps
        .map((app) => _localStorageService.convertToCustomer(app))
        .toList();
  }

  Future<Customer?> _getLocalApplication(String id) async {
    final app = await _localStorageService.getApplication(id);
    return app != null ? _localStorageService.convertToCustomer(app) : null;
  }

  Future<List<Customer>> _getPendingApplications() async {
    // Get applications with pending sync status
    final allApps = await _getLocalApplications();
    return allApps
        .where(
          (app) =>
              app.syncStatus == SyncStatus.pending.name ||
              app.syncStatus == SyncStatus.created.name ||
              app.syncStatus == SyncStatus.updated.name,
        )
        .toList();
  }

  Future<ApplicationSyncResult> _syncApplicationToServer(String localId) async {
    try {
      final localApp = await _getLocalApplication(localId);
      if (localApp == null) {
        return ApplicationSyncResult.failure('Local application not found');
      }

      final response = await _apiService.post(
        ApiConfig.createApplication,
        data: _convertToApiFormat(localApp),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final serverData = response.data;
        return ApplicationSyncResult.success(serverId: serverData['id']);
      }
    } on ApiException catch (e) {
      return ApplicationSyncResult.failure(e.message);
    } catch (e) {
      return ApplicationSyncResult.failure('Sync failed: ${e.toString()}');
    }

    return ApplicationSyncResult.failure('Sync failed');
  }

  Future<Customer?> _getApplicationFromServer(String serverId) async {
    try {
      final response = await _apiService.get(
        '${ApiConfig.applications}/$serverId',
      );

      if (response.statusCode == 200) {
        return _convertFromApiFormat(response.data);
      }
    } catch (e) {
      Logger.e('Error getting application from server: $e');
    }
    return null;
  }

  Future<void> _pullApplicationsFromServer() async {
    try {
      final response = await _apiService.get(ApiConfig.getApplications);

      if (response.statusCode == 200) {
        final applications =
            (response.data['applications'] as List)
                .map((app) => _convertFromApiFormat(app))
                .toList();

        // Update local storage with server data
        for (final app in applications) {
          await _updateApplicationLocally(app, SyncStatus.synced);
        }
      }
    } catch (e) {
      Logger.e('Error pulling applications from server: $e');
    }
  }

  Map<String, dynamic> _convertToApiFormat(Customer customer) {
    return {
      'id_card_type': customer.idCardType?.toString().split('.').last,
      'id_number': customer.idNumber,
      'full_name_khmer': customer.fullNameKhmer,
      'full_name_latin': customer.fullNameLatin,
      'phone': customer.phone,
      'date_of_birth': customer.dateOfBirth?.toIso8601String(),
      'portfolio_officer_name': customer.portfolioOfficerName,
      'requested_amount': customer.requestedAmount,
      'loan_purposes': customer.loanPurposes?.map((e) => e.toString().split('.').last).toList(),
      'purpose_details': customer.purposeDetails,
      'product_type': customer.productType?.toString().split('.').last,
      'desired_loan_term': customer.desiredLoanTerm,
      'requested_disbursement_date':
          customer.requestedDisbursementDate?.toIso8601String(),
      'guarantor_name': customer.guarantorName,
      'guarantor_phone': customer.guarantorPhone,
      'borrower_nid_photo': customer.borrowerNidPhotoPath,
      'borrower_home_photo': customer.borrowerHomeOrLandPhotoPath,
      'borrower_business_photo': customer.borrowerBusinessPhotoPath,
      'guarantor_nid_photo': customer.guarantorNidPhotoPath,
      'guarantor_home_photo': customer.guarantorHomeOrLandPhotoPath,
      'guarantor_business_photo': customer.guarantorBusinessPhotoPath,
      'profile_photo': customer.profilePhotoPath,
      'collaterals': customer.collaterals,
      'documents': customer.documents,
    };
  }

  IdCardType? _parseIdCardType(String? type) {
    if (type == null) return null;
    return IdCardType.values.firstWhere(
      (e) => e.toString().split('.').last == type,
      orElse: () => IdCardType.cambodianIdentity,
    );
  }

  List<LoanPurposeType>? _parseLoanPurposes(dynamic purposes) {
    if (purposes == null) return null;
    if (purposes is List) {
      return purposes.map((e) {
        if (e is String) {
          return LoanPurposeType.values.firstWhere(
            (purpose) => purpose.toString().split('.').last == e,
            orElse: () => LoanPurposeType.other,
          );
        }
        return LoanPurposeType.other;
      }).toList();
    }
    return null;
  }

  product_type.ProductType? _parseProductType(String? type) {
    if (type == null) return null;
    return product_type.ProductType.values.firstWhere(
      (e) => e.toString().split('.').last == type,
      orElse: () => product_type.ProductType.microLoan,
    );
  }

  List<Map<String, dynamic>>? _parseCollaterals(dynamic collaterals) {
    if (collaterals == null) return null;
    if (collaterals is List) {
      return List<Map<String, dynamic>>.from(collaterals);
    }
    return null;
  }

  List<Map<String, dynamic>>? _parseDocuments(dynamic documents) {
    if (documents == null) return null;
    if (documents is List) {
      return List<Map<String, dynamic>>.from(documents);
    }
    return null;
  }

  Customer _convertFromApiFormat(Map<String, dynamic> data) {
    return Customer(
      id: data['local_id'] ?? data['id'].toString(),
      serverId: data['id'].toString(),
      name: data['full_name_latin'] ?? data['full_name_khmer'] ?? '',
      phone: data['phone'] ?? '',
      nid: data['id_number'] ?? '',
      loanAmount: (data['requested_amount'] ?? 0).toDouble(),
      loanStartDate:
          DateTime.tryParse(data['created_at'] ?? '') ?? DateTime.now(),
      loanStatus: _parseLoanStatus(data['status']),
      syncStatus: SyncStatus.synced.name,
      idCardType: _parseIdCardType(data['id_card_type']),
      fullNameKhmer: data['full_name_khmer'],
      fullNameLatin: data['full_name_latin'],
      dateOfBirth: DateTime.tryParse(data['date_of_birth'] ?? ''),
      idNumber: data['id_number'],
      portfolioOfficerName: data['portfolio_officer_name'],
      requestedAmount: (data['requested_amount'] ?? 0).toDouble(),
      loanPurposes: _parseLoanPurposes(data['loan_purposes']),
      purposeDetails: data['purpose_details'],
      productType: _parseProductType(data['product_type']),
      desiredLoanTerm: data['desired_loan_term'],
      requestedDisbursementDate: DateTime.tryParse(
        data['requested_disbursement_date'] ?? '',
      ),
      guarantorName: data['guarantor_name'],
      guarantorPhone: data['guarantor_phone'],
      borrowerNidPhotoPath: data['borrower_nid_photo'],
      borrowerHomeOrLandPhotoPath: data['borrower_home_photo'],
      borrowerBusinessPhotoPath: data['borrower_business_photo'],
      guarantorNidPhotoPath: data['guarantor_nid_photo'],
      guarantorHomeOrLandPhotoPath: data['guarantor_home_photo'],
      guarantorBusinessPhotoPath: data['guarantor_business_photo'],
      profilePhotoPath: data['profile_photo'],
      collaterals: _parseCollaterals(data['collaterals']),
      documents: _parseDocuments(data['documents']),
    );
  }

  LoanStatus _parseLoanStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'draft':
        return LoanStatus.draft;
      case 'pending':
        return LoanStatus.pending;
      case 'approved':
        return LoanStatus.approved;
      case 'disbursed':
        return LoanStatus.disbursed;
      case 'completed':
        return LoanStatus.completed;
      case 'rejected':
        return LoanStatus.rejected;
      default:
        return LoanStatus.pending;
    }
  }

  // Using the existing _parseIdCardType method instead
}

// Result classes
class ApplicationResult {
  final bool isSuccess;
  final Customer? application;
  final String? error;

  ApplicationResult._({required this.isSuccess, this.application, this.error});

  factory ApplicationResult.success(Customer? application) =>
      ApplicationResult._(isSuccess: true, application: application);

  factory ApplicationResult.failure(String error) =>
      ApplicationResult._(isSuccess: false, error: error);
}

class ApplicationSyncResult {
  final bool isSuccess;
  final String? serverId;
  final String? error;

  ApplicationSyncResult._({required this.isSuccess, this.serverId, this.error});

  factory ApplicationSyncResult.success({required String serverId}) =>
      ApplicationSyncResult._(isSuccess: true, serverId: serverId);

  factory ApplicationSyncResult.failure(String error) =>
      ApplicationSyncResult._(isSuccess: false, error: error);
}

class SyncResult {
  final bool isSuccess;
  final int successCount;
  final int failureCount;
  final List<String> errors;
  final String? error;

  SyncResult._({
    required this.isSuccess,
    this.successCount = 0,
    this.failureCount = 0,
    this.errors = const [],
    this.error,
  });

  factory SyncResult.success({
    required int successCount,
    required int failureCount,
    required List<String> errors,
  }) => SyncResult._(
    isSuccess: true,
    successCount: successCount,
    failureCount: failureCount,
    errors: errors,
  );

  factory SyncResult.failure(String error) =>
      SyncResult._(isSuccess: false, error: error);
}

class FileUploadResult {
  final bool isSuccess;
  final String? fileId;
  final String? fileUrl;
  final String? error;

  FileUploadResult._({
    required this.isSuccess,
    this.fileId,
    this.fileUrl,
    this.error,
  });

  factory FileUploadResult.success({
    required String fileId,
    required String fileUrl,
  }) => FileUploadResult._(isSuccess: true, fileId: fileId, fileUrl: fileUrl);

  factory FileUploadResult.failure(String error) =>
      FileUploadResult._(isSuccess: false, error: error);
}

class FileDownloadResult {
  final bool isSuccess;
  final Uint8List? fileData;
  final String? error;

  FileDownloadResult._({required this.isSuccess, this.fileData, this.error});

  factory FileDownloadResult.success(Uint8List fileData) =>
      FileDownloadResult._(isSuccess: true, fileData: fileData);

  factory FileDownloadResult.failure(String error) =>
      FileDownloadResult._(isSuccess: false, error: error);
}
