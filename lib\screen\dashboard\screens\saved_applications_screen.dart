import 'package:flutter/material.dart';
import 'package:lc_work_flow/services/local_storage_service.dart';
import 'package:intl/intl.dart';
import 'package:get/get.dart'; // Added for Get.to()
import 'package:lc_work_flow/models/customer_model.dart'; // Import Customer model
import 'package:lc_work_flow/screen/dashboard/screens/customer_details_screen.dart';

class SavedApplicationsScreen extends StatefulWidget {
  const SavedApplicationsScreen({super.key});

  @override
  State<SavedApplicationsScreen> createState() =>
      _SavedApplicationsScreenState();
}

class _SavedApplicationsScreenState extends State<SavedApplicationsScreen> {
  final LocalStorageService _localStorageService = LocalStorageService();
  List<Map<String, dynamic>> _applications = [];
  bool _isLoading = true;
  String _selectedTab = 'submitted'; // 'draft' or 'submitted'

  @override
  void initState() {
    super.initState();
    _loadApplications();
  }

  Future<void> _loadApplications() async {
    setState(() => _isLoading = true);

    try {
      List<Map<String, dynamic>> applications;
      if (_selectedTab == 'draft') {
        applications = await _localStorageService.getDraftApplications();
      } else {
        applications = await _localStorageService.getSubmittedApplications();
      }

      setState(() {
        _applications = applications;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading applications: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Saved Applications'),
        backgroundColor: const Color(0xFF12306E),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Tab selector
          Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() => _selectedTab = 'submitted');
                      _loadApplications();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color:
                            _selectedTab == 'submitted'
                                ? const Color(0xFF12306E)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Submitted',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color:
                              _selectedTab == 'submitted'
                                  ? Colors.white
                                  : Colors.grey[600],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() => _selectedTab = 'draft');
                      _loadApplications();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color:
                            _selectedTab == 'draft'
                                ? const Color(0xFF12306E)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Drafts',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color:
                              _selectedTab == 'draft'
                                  ? Colors.white
                                  : Colors.grey[600],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Applications list
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _applications.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.folder_open,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No $_selectedTab applications found',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    )
                    : RefreshIndicator(
                      onRefresh: _loadApplications,
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: _applications.length,
                        itemBuilder: (context, index) {
                          final application = _applications[index];
                          final customer = _localStorageService
                              .convertToCustomer(application);
                          return _buildApplicationCard(customer);
                        },
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildApplicationCard(Customer customer) {
    final name = customer.displayName;
    final amount =
        customer.requestedAmount?.toString() ?? customer.loanAmount.toString();
    final createdAt = customer.loanStartDate;
    final status = customer.loanStatus.toString().split('.').last;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          Get.to(() => CustomerDetailsScreen(customer: customer));
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // Display Loan Status
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: customer.statusColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      status.toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Display Sync Status
                  Tooltip(
                    message:
                        customer.syncStatus != null
                            ? 'Sync Status: ${customer.syncStatus!.split('.').last}'
                            : 'Sync Status: Unknown',
                    child: Icon(
                      customer.syncStatusIcon,
                      color: customer.syncStatusColor,
                      size: 20,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.monetization_on,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${NumberFormat('#,##0.00', 'en_US').format(double.parse(amount))}៛',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    DateFormat('MMM dd, yyyy').format(createdAt),
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
              if (customer.phone.isNotEmpty) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      customer.phone,
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
