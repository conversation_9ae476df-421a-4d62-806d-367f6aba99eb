import 'package:lc_work_flow/models/customer_model.dart';
import 'package:lc_work_flow/models/dto/application_dto.dart';
import 'package:lc_work_flow/models/product_type.dart' as product_type;

/// Converter class to transform between Customer model and ApplicationDto
class ApplicationConverter {
  /// Convert Customer model to ApplicationDto for API communication
  static ApplicationDto toDto(Customer customer) {
    return ApplicationDto(
      id: customer.serverId, // Use serverId for API communication
      status: customer.loanStatus.name,
      idCardType: customer.idCardType?.name,
      idNumber: customer.idNumber ?? customer.nid,
      fullNameKhmer: customer.fullNameKhmer,
      fullNameLatin: customer.fullNameLatin ?? customer.name,
      phone: customer.phone,
      dateOfBirth: customer.dateOfBirth?.toIso8601String(),
      portfolioOfficerName: customer.portfolioOfficerName,
      requestedAmount: customer.requestedAmount ?? customer.loanAmount,
      loanPurposes: customer.loanPurposes?.map((e) => e.name).toList(),
      purposeDetails: customer.purposeDetails ?? customer.loanPurpose,
      productType: customer.productType?.name,
      desiredLoanTerm: customer.desiredLoanTerm,
      requestedDisbursementDate:
          customer.requestedDisbursementDate?.toIso8601String(),
      guarantorName: customer.guarantorName,
      guarantorPhone: customer.guarantorPhone,
      borrowerNidPhoto: customer.borrowerNidPhotoPath,
      borrowerHomePhoto: customer.borrowerHomeOrLandPhotoPath,
      borrowerBusinessPhoto: customer.borrowerBusinessPhotoPath,
      guarantorNidPhoto: customer.guarantorNidPhotoPath,
      guarantorHomePhoto: customer.guarantorHomeOrLandPhotoPath,
      guarantorBusinessPhoto: customer.guarantorBusinessPhotoPath,
      profilePhoto: customer.profilePhotoPath ?? customer.profileImage,
      collaterals: customer.collaterals,
      documents: customer.documents,
      syncStatus: customer.syncStatus,
      createdAt: customer.loanStartDate.toIso8601String(),
      updatedAt: DateTime.now().toIso8601String(),
      version: 1, // Default version for new applications
    );
  }

  /// Convert ApplicationDto to Customer model for local storage
  static Customer fromDto(ApplicationDto dto, {String? localId}) {
    return Customer(
      id: localId ?? dto.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      serverId: dto.id,
      name: dto.fullNameLatin ?? '',
      phone: dto.phone ?? '',
      nid: dto.idNumber ?? '',
      profileImage: dto.profilePhoto,
      loanAmount: dto.requestedAmount ?? 0.0,
      loanStartDate:
          dto.createdAt != null
              ? DateTime.tryParse(dto.createdAt!) ?? DateTime.now()
              : DateTime.now(),
      loanEndDate: null, // Not provided in DTO
      loanStatus: _parseLoanStatus(dto.status),
      interestRate: null, // Not provided in DTO
      loanPurpose: dto.purposeDetails,
      syncStatus: dto.syncStatus,

      // Extended fields from DTO
      idCardType: _parseIdCardType(dto.idCardType),
      idCardPhotoPath:
          dto.idCardImages?.isNotEmpty == true ? dto.idCardImages!.first : null,
      fullNameKhmer: dto.fullNameKhmer,
      fullNameLatin: dto.fullNameLatin,
      dateOfBirth:
          dto.dateOfBirth != null ? DateTime.tryParse(dto.dateOfBirth!) : null,
      idNumber: dto.idNumber,
      portfolioOfficerName: dto.portfolioOfficerName,
      requestedAmount: dto.requestedAmount,
      loanPurposes:
          dto.loanPurposes?.map((e) => _parseLoanPurposeType(e)).toList(),
      purposeDetails: dto.purposeDetails,
      productType: _parseProductType(dto.productType),
      desiredLoanTerm: dto.desiredLoanTerm,
      requestedDisbursementDate:
          dto.requestedDisbursementDate != null
              ? DateTime.tryParse(dto.requestedDisbursementDate!)
              : null,
      borrowerNidPhotoPath: dto.borrowerNidPhoto,
      borrowerHomeOrLandPhotoPath: dto.borrowerHomePhoto,
      borrowerBusinessPhotoPath: dto.borrowerBusinessPhoto,
      guarantorName: dto.guarantorName,
      guarantorPhone: dto.guarantorPhone,
      guarantorNidPhotoPath: dto.guarantorNidPhoto,
      guarantorHomeOrLandPhotoPath: dto.guarantorHomePhoto,
      guarantorBusinessPhotoPath: dto.guarantorBusinessPhoto,
      profilePhotoPath: dto.profilePhoto,
      collaterals: dto.collaterals,
      documents: dto.documents,
    );
  }

  /// Parse loan status from string
  static LoanStatus _parseLoanStatus(String status) {
    switch (status.toLowerCase()) {
      case 'draft':
        return LoanStatus.draft;
      case 'pending':
        return LoanStatus.pending;
      case 'approved':
        return LoanStatus.approved;
      case 'disbursed':
        return LoanStatus.disbursed;
      case 'completed':
        return LoanStatus.completed;
      case 'rejected':
        return LoanStatus.rejected;
      default:
        return LoanStatus.pending;
    }
  }

  /// Parse ID card type from string
  static IdCardType? _parseIdCardType(String? type) {
    if (type == null) return null;
    switch (type.toLowerCase()) {
      case 'nid':
        return IdCardType.nid;
      case 'passport':
        return IdCardType.passport;
      case 'driverlicense':
        return IdCardType.driverLicense;
      case 'cambodianidentity':
        return IdCardType.cambodianIdentity;
      default:
        return IdCardType.cambodianIdentity;
    }
  }

  /// Parse loan purpose type from string
  static LoanPurposeType _parseLoanPurposeType(String type) {
    switch (type.toLowerCase()) {
      case 'agriculture':
        return LoanPurposeType.agriculture;
      case 'commerce':
        return LoanPurposeType.commerce;
      case 'services':
        return LoanPurposeType.services;
      case 'transportation':
        return LoanPurposeType.transportation;
      case 'construction':
        return LoanPurposeType.construction;
      case 'family':
        return LoanPurposeType.family;
      case 'other':
        return LoanPurposeType.other;
      default:
        return LoanPurposeType.agriculture;
    }
  }

  /// Parse product type from string
  static product_type.ProductType? _parseProductType(String? type) {
    if (type == null) return null;
    switch (type.toLowerCase()) {
      case 'daily':
        return product_type.ProductType.daily;
      case 'weekly':
        return product_type.ProductType.weekly;
      case 'biweekly':
        return product_type.ProductType.biweekly;
      case 'monthly':
        return product_type.ProductType.monthly;
      case 'microloan':
        return product_type.ProductType.microLoan;
      default:
        return product_type.ProductType.daily;
    }
  }
}
