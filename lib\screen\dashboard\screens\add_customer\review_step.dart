// ignore_for_file: public_member_api_docs

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class ReviewStep extends StatelessWidget {
  final Map<String, dynamic> borrowerInfo;
  final Map<String, dynamic> loanInfo;
  final List<Map<String, dynamic>> collateralList;
  final Map<String, dynamic>? guarantor;
  final List<Map<String, dynamic>> documents;
  final Map<String, dynamic> photos;
  final String officerName;
  final VoidCallback onBack;
  final VoidCallback onConfirm;

  const ReviewStep({
    super.key,
    required this.borrowerInfo,
    required this.loanInfo,
    required this.collateralList,
    required this.guarantor,
    required this.documents,
    required this.photos,
    required this.officerName,
    required this.onBack,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with customer name
        _buildHeader(context),
        const SizedBox(height: 16),

        // Basic Information sections
        _buildBorrowerInfoSection(context),
        const SizedBox(height: 16),
        _buildLoanInfoSection(context),
        if (guarantor != null) ...[
          const SizedBox(height: 16),
          _buildGuarantorSection(context),
        ],
        if (officerName.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildOfficerSection(context),
        ],
        const SizedBox(height: 24),

        // Photo Albums Section
        Text(
          'រូបថត និងឯកសារ',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: const Color(0xFF12306E),
          ),
        ),
        const SizedBox(height: 16),
        _buildCollateralSection(context),
        const SizedBox(height: 16),
        _buildDocumentsSection(context),
        const SizedBox(height: 16),
        _buildPhotosSection(context),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    final String khmerName = borrowerInfo['fullNameKhmer']?.toString() ?? '';
    final String latinName = borrowerInfo['fullNameLatin']?.toString() ?? '';
    final String displayName = khmerName.isNotEmpty ? khmerName : latinName;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF12306E), Color(0xFF1E4A8C)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.person_outline, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'ពិនិត្យមើលព័ត៌មាន',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      displayName.isNotEmpty ? displayName : 'No Name Provided',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBorrowerInfoSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ព័ត៌មានអតិថិជន',
      icon: Icons.person,
      children: [
        _buildInfoRow('ប្រភេទឯកសារ', borrowerInfo['documentType']),
        _buildInfoRow('លេខអត្តសញ្ញាណ', borrowerInfo['idNumber']),
        _buildInfoRow('ឈ្មោះពេញ (ខ្មែរ)', borrowerInfo['fullNameKhmer']),
        _buildInfoRow('ឈ្មោះពេញ (ឡាតាំង)', borrowerInfo['fullNameLatin']),
        _buildInfoRow('លេខទូរស័ព្ទ', borrowerInfo['phone']),
        _buildInfoRow('ថ្ងៃខែឆ្នាំកំណើត', borrowerInfo['dateOfBirth']),
      ],
    );
  }

  Widget _buildLoanInfoSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ព័ត៌មានកម្ចី',
      icon: Icons.account_balance_wallet,
      children: [
        _buildInfoRow(
          'ចំនួនទឹកប្រាក់ស្នើសុំ',
          loanInfo['requestedAmount']?.toString() != null &&
                  loanInfo['requestedAmount'].toString().isNotEmpty
              ? '${NumberFormat('#,##0.00', 'en_US').format(loanInfo['requestedAmount'])}៛'
              : null,
        ),
        _buildInfoRow('ប្រភេទផលិតផល', loanInfo['productType']),
        _buildInfoRow('រយៈពេលកម្ចី', loanInfo['loanTerm']),
        _buildInfoRow('ថ្ងៃចេញប្រាក់', loanInfo['disbursementDate']),
      ],
    );
  }

  Widget _buildCollateralSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ធានា',
      icon: Icons.security,
      children:
          collateralList.isEmpty
              ? [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.grey),
                      SizedBox(width: 8),
                      Text(
                        'មិនមានធានាត្រូវបានជ្រើសរើស',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ]
              : collateralList.map((collateral) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.blue,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          collateral['type']?.toString() ?? '',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                      if (collateral['image'] != null)
                        _buildImageThumbnail(collateral['image']),
                    ],
                  ),
                );
              }).toList(),
    );
  }

  Widget _buildGuarantorSection(BuildContext context) {
    if (guarantor == null) return const SizedBox.shrink();

    return _buildSection(
      context: context,
      title: 'អ្នកធានា',
      icon: Icons.people,
      children: [
        _buildInfoRow('ឈ្មោះ', guarantor!['name']),
        _buildInfoRow('លេខទូរស័ព្ទ', guarantor!['phone']),
      ],
    );
  }

  Widget _buildDocumentsSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ឯកសារយោង',
      icon: Icons.description,
      children:
          documents.isEmpty
              ? [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.grey),
                      SizedBox(width: 8),
                      Text(
                        'មិនមានឯកសារត្រូវបានបញ្ចូល',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ]
              : documents.map((doc) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green[200]!),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.description,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          doc['type']?.toString() ?? 'Document',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                      if (doc['image'] != null)
                        _buildImageThumbnail(doc['image']),
                    ],
                  ),
                );
              }).toList(),
    );
  }

  Widget _buildPhotosSection(BuildContext context) {
    return Column(
      children: [
        // Borrower Photo Album
        _buildPhotoAlbum(
          context: context,
          title: 'រូបថតអ្នកខ្ចី',
          subtitle: 'Borrower Photos',
          icon: Icons.person,
          color: Colors.blue,
          photos: {
            'borrowerNidPhoto': photos['borrowerNidPhoto'],
            'borrowerHomePhoto': photos['borrowerHomePhoto'],
            'borrowerBusinessPhoto': photos['borrowerBusinessPhoto'],
            'profilePhoto': photos['profilePhoto'],
          },
        ),
        const SizedBox(height: 16),

        // Guarantor Photo Album (if guarantor exists)
        if (guarantor != null) ...[
          _buildPhotoAlbum(
            context: context,
            title: 'រូបថតអ្នកធានា',
            subtitle: 'Guarantor Photos',
            icon: Icons.people,
            color: Colors.green,
            photos: {
              'guarantorNidPhoto': photos['guarantorNidPhoto'],
              'guarantorHomePhoto': photos['guarantorHomePhoto'],
              'guarantorBusinessPhoto': photos['guarantorBusinessPhoto'],
            },
          ),
          const SizedBox(height: 16),
        ],

        // Collateral Photo Album
        _buildCollateralPhotoAlbum(context),
        const SizedBox(height: 16),

        // Documents Album
        _buildDocumentAlbum(context),
      ],
    );
  }

  Widget _buildOfficerSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ព័ត៌មានមន្ត្រី',
      icon: Icons.badge,
      children: [_buildInfoRow('ឈ្មោះមន្ត្រីគ្រប់គ្រង', officerName)],
    );
  }

  // Helper methods
  Widget _buildSection({
    required BuildContext context,
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF12306E).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: const Color(0xFF12306E), size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF12306E),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, dynamic value) {
    if (value == null || value.toString().trim().isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value.toString(),
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageThumbnail(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) {
      return Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Icon(
          Icons.image_not_supported,
          color: Colors.grey,
          size: 20,
        ),
      );
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.file(
          File(imagePath),
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: Colors.grey[200],
              child: const Icon(
                Icons.broken_image,
                color: Colors.grey,
                size: 20,
              ),
            );
          },
        ),
      ),
    );
  }



  // Photo Album Methods
  Widget _buildPhotoAlbum({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required Map<String, dynamic> photos,
  }) {
    // Flatten photos to handle both single images and lists
    final validPhotos = <MapEntry<String, dynamic>>[];
    for (final entry in photos.entries) {
      if (entry.value != null) {
        if (entry.value is List) {
          // Handle multiple images for the same type
          final imageList = entry.value as List;
          for (int i = 0; i < imageList.length; i++) {
            if (imageList[i] != null && imageList[i].toString().isNotEmpty) {
              validPhotos.add(
                MapEntry(
                  '${entry.key}_${i + 1}', // Add index to make unique
                  imageList[i],
                ),
              );
            }
          }
        } else if (entry.value.toString().isNotEmpty) {
          // Handle single image
          validPhotos.add(entry);
        }
      }
    }

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Album Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: color.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${validPhotos.length}',
                    style: TextStyle(
                      color: color,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Album Content
          Padding(
            padding: const EdgeInsets.all(16),
            child:
                validPhotos.isEmpty
                    ? _buildEmptyAlbumState(color)
                    : _buildPhotoGrid(validPhotos),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyAlbumState(Color color) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(Icons.photo_library_outlined, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 12),
          Text(
            'មិនមានរូបថត',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            'No photos available',
            style: TextStyle(color: Colors.grey[500], fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoGrid(List<MapEntry<String, dynamic>> photoEntries) {
    if (photoEntries.length == 1) {
      // Single image - show larger
      return _buildSinglePhotoView(photoEntries.first);
    } else if (photoEntries.length <= 4) {
      // Small grid for 2-4 images
      return _buildSmallGrid(photoEntries);
    } else {
      // Carousel view for many images
      return _buildPhotoCarousel(photoEntries);
    }
  }

  Widget _buildSinglePhotoView(MapEntry<String, dynamic> photoEntry) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: _buildAlbumPhotoItem(
        _getPhotoLabel(photoEntry.key),
        photoEntry.value.toString(),
        isLarge: true,
      ),
    );
  }

  Widget _buildSmallGrid(List<MapEntry<String, dynamic>> photoEntries) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: photoEntries.length == 2 ? 2 : 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: photoEntries.length,
      itemBuilder: (context, index) {
        final entry = photoEntries[index];
        return _buildAlbumPhotoItem(
          _getPhotoLabel(entry.key),
          entry.value.toString(),
        );
      },
    );
  }

  Widget _buildPhotoCarousel(List<MapEntry<String, dynamic>> photoEntries) {
    return Column(
      children: [
        // Main carousel
        SizedBox(
          height: 200,
          child: PageView.builder(
            itemCount: photoEntries.length,
            itemBuilder: (context, index) {
              final entry = photoEntries[index];
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 8),
                child: _buildAlbumPhotoItem(
                  _getPhotoLabel(entry.key),
                  entry.value.toString(),
                  isLarge: true,
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 12),
        // Thumbnail strip
        SizedBox(
          height: 60,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: photoEntries.length,
            itemBuilder: (context, index) {
              final entry = photoEntries[index];
              return Container(
                margin: const EdgeInsets.only(right: 8),
                child: _buildThumbnailItem(
                  entry.value.toString(),
                  _getPhotoLabel(entry.key),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAlbumPhotoItem(
    String label,
    String imagePath, {
    bool isLarge = false,
  }) {
    return Builder(
      builder:
          (context) => GestureDetector(
            onTap: () {
              _showPhotoPreview(context, imagePath);
            },
            child: Column(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(isLarge ? 16 : 12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: isLarge ? 8 : 6,
                          offset: Offset(0, isLarge ? 4 : 3),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(isLarge ? 16 : 12),
                      child: Stack(
                        children: [
                          Image.file(
                            File(imagePath),
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey[200],
                                child: Icon(
                                  Icons.broken_image,
                                  color: Colors.grey,
                                  size: isLarge ? 50 : 30,
                                ),
                              );
                            },
                          ),
                          // Overlay for better text visibility
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              padding: EdgeInsets.all(isLarge ? 12 : 8),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.black.withValues(alpha: 0.7),
                                  ],
                                ),
                              ),
                              child: Text(
                                label,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: isLarge ? 14 : 10,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                          // Add a zoom indicator for large images
                          if (isLarge)
                            Positioned(
                              top: 8,
                              right: 8,
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: Colors.black54,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Icon(
                                  Icons.zoom_in,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildThumbnailItem(String imagePath, String label) {
    return Builder(
      builder:
          (context) => GestureDetector(
            onTap: () {
              _showPhotoPreview(context, imagePath);
            },
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: Stack(
                  children: [
                    Image.file(
                      File(imagePath),
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[200],
                          child: const Icon(
                            Icons.broken_image,
                            color: Colors.grey,
                            size: 20,
                          ),
                        );
                      },
                    ),
                    // Small label overlay
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withValues(alpha: 0.8),
                            ],
                          ),
                        ),
                        child: Text(
                          label,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
    );
  }

  Widget _buildCollateralPhotoAlbum(BuildContext context) {
    // Group collateral images by type - support multiple images per type
    final collateralPhotos = <String, dynamic>{};

    for (final collateral in collateralList) {
      if (collateral['image'] != null &&
          collateral['image'].toString().isNotEmpty) {
        final type = collateral['type']?.toString() ?? 'Unknown';
        final imagePath = collateral['image'].toString();

        // If type already exists, convert to list or add to existing list
        if (collateralPhotos.containsKey(type)) {
          if (collateralPhotos[type] is String) {
            // Convert single image to list
            collateralPhotos[type] = [collateralPhotos[type], imagePath];
          } else if (collateralPhotos[type] is List) {
            // Add to existing list
            (collateralPhotos[type] as List).add(imagePath);
          }
        } else {
          // First image for this type
          collateralPhotos[type] = imagePath;
        }
      }
    }

    return _buildPhotoAlbum(
      context: context,
      title: 'រូបថតធានា',
      subtitle: 'Collateral Photos',
      icon: Icons.security,
      color: Colors.orange,
      photos: collateralPhotos,
    );
  }

  Widget _buildDocumentAlbum(BuildContext context) {
    final documentPhotos = <String, dynamic>{};

    for (final doc in documents) {
      if (doc['image'] != null && doc['image'].toString().isNotEmpty) {
        final type = doc['type']?.toString() ?? 'Document';
        final imagePath = doc['image'].toString();

        // Support multiple documents of the same type
        if (documentPhotos.containsKey(type)) {
          if (documentPhotos[type] is String) {
            // Convert single image to list
            documentPhotos[type] = [documentPhotos[type], imagePath];
          } else if (documentPhotos[type] is List) {
            // Add to existing list
            (documentPhotos[type] as List).add(imagePath);
          }
        } else {
          // First document for this type
          documentPhotos[type] = imagePath;
        }
      }
    }

    return _buildPhotoAlbum(
      context: context,
      title: 'ឯកសារយោង',
      subtitle: 'Documents',
      icon: Icons.description,
      color: Colors.purple,
      photos: documentPhotos,
    );
  }

  void _showPhotoPreview(BuildContext context, String imagePath) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder:
          (context) => Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.8,
                maxWidth: MediaQuery.of(context).size.width * 0.9,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with close button
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.8),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Photo Preview',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(Icons.close, color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                  // Image container
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.9),
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(16),
                          bottomRight: Radius.circular(16),
                        ),
                      ),
                      child: Center(
                        child: InteractiveViewer(
                          panEnabled: true,
                          boundaryMargin: const EdgeInsets.all(20),
                          minScale: 0.5,
                          maxScale: 4.0,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(
                              File(imagePath),
                              fit: BoxFit.contain,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  height: 200,
                                  width: 200,
                                  color: Colors.grey[800],
                                  child: const Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.broken_image,
                                        color: Colors.white,
                                        size: 50,
                                      ),
                                      SizedBox(height: 8),
                                      Text(
                                        'Image not found',
                                        style: TextStyle(color: Colors.white),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }
  String _getPhotoLabel(String photoKey) {
    switch (photoKey) {
      case 'borrowerNidPhoto':
        return 'អត្តសញ្ញាណប័ណ្ណអ្នកខ្ចី';
      case 'borrowerHomePhoto':
        return 'រូបថតផ្ទះអ្នកខ្ចី';
      case 'borrowerBusinessPhoto':
        return 'រូបថតអាជីវកម្មអ្នកខ្ចី';
      case 'profilePhoto':
        return 'រូបថតផ្ទាល់ខ្លួន';
      case String collateralKey when collateralKey.startsWith('collateral_'):
        return 'រូបថតធានា (${collateralKey.substring(11).replaceAll('_', ' ')})';
      case String documentKey when documentKey.startsWith('document_'):
        return 'ឯកសារ (${documentKey.substring(9).replaceAll('_', ' ')})';
      case 'guarantorNidPhoto':
        return 'អត្តសញ្ញាណប័ណ្ណអ្នកធានា';
      case 'guarantorHomePhoto':
        return 'រូបថតផ្ទះអ្នកធានា';
      case 'guarantorBusinessPhoto':
        return 'រូបថតអាជីវកម្មអ្នកធានា';
      default:
        return photoKey.replaceAll('_', ' ');
    }
  }
}
