import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:lc_work_flow/core/config/api_config.dart';
import 'package:lc_work_flow/core/utils/logger.dart';
import 'package:lc_work_flow/services/auth_service.dart';
import 'package:lc_work_flow/services/interfaces/api_service_interface.dart';
import 'package:lc_work_flow/models/dto/application_dto.dart';
import 'package:lc_work_flow/models/dto/auth_response_dto.dart';
import 'package:lc_work_flow/models/dto/sync_status_dto.dart';
import 'package:lc_work_flow/models/dto/dashboard_stats_dto.dart';

class ApiService implements IApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal() {
    _secureStorage = const FlutterSecureStorage();
    _connectivity = Connectivity();
    _dio = Dio(
      BaseOptions(
        baseUrl: ApiConfig.baseUrl,
        connectTimeout: const Duration(milliseconds: ApiConfig.connectTimeout),
        receiveTimeout: const Duration(milliseconds: ApiConfig.receiveTimeout),
        sendTimeout: const Duration(milliseconds: ApiConfig.sendTimeout),
        headers: ApiConfig.defaultHeaders,
        validateStatus: (status) {
          return status != null && status < 500;
        },
      ),
    );
    _setupInterceptors();
  }

  late final Dio _dio;
  late final FlutterSecureStorage _secureStorage;
  late final Connectivity _connectivity;

  void _setupInterceptors() {
    // Auth interceptor - Add auth token and handle token refresh
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Skip auth for login and refresh endpoints
          if (!options.path.contains('/auth/token') &&
              !options.path.contains('/auth/refresh')) {
            final token = await _secureStorage.read(key: 'access_token');
            if (token != null) {
              options.headers['Authorization'] = 'Bearer $token';
              Logger.d('Using token: $token'); // Add this line for debugging
            }
          }

          // Add request timestamp for debugging
          if (!ApiConfig.isProduction) {
            options.headers['X-Request-Time'] =
                DateTime.now().toIso8601String();
          }

          handler.next(options);
        },
        onResponse: (response, handler) async {
          // Log successful responses in debug mode
          if (!ApiConfig.isProduction) {
            Logger.d(
              '✅ ${response.requestOptions.method} ${response.requestOptions.path} - ${response.statusCode}',
            );
          }
          handler.next(response);
        },
        onError: (error, handler) async {
          // Handle 401 Unauthorized - Token expired
          if (error.response?.statusCode == 401 &&
              !error.requestOptions.path.contains('/auth/')) {
            final refreshed = await _refreshToken();
            if (refreshed) {
              // Retry the original request with new token
              final token = await _secureStorage.read(key: 'access_token');
              error.requestOptions.headers['Authorization'] = 'Bearer $token';
              try {
                final response = await _dio.fetch(error.requestOptions);
                handler.resolve(response);
                return;
              } catch (retryError) {
                // If retry fails, proceed with original error
              }
            } else {
              // Refresh failed, logout user
              await AuthService().logout();
            }
          }

          // Log errors in debug mode
          if (!ApiConfig.isProduction) {
            Logger.e(
              '❌ ${error.requestOptions.method} ${error.requestOptions.path} - ${error.response?.statusCode ?? 'Network Error'}',
            );
            if (error.response?.data != null) {
              Logger.e('Error data: ${error.response?.data}');
            }
          }

          handler.next(error);
        },
      ),
    );

    // Retry interceptor for network failures
    _dio.interceptors.add(
      InterceptorsWrapper(
        onError: (error, handler) async {
          if (_shouldRetry(error) &&
              error.requestOptions.extra['retryCount'] == null) {
            error.requestOptions.extra['retryCount'] = 0;
          }

          final retryCount = error.requestOptions.extra['retryCount'] ?? 0;
          if (_shouldRetry(error) && retryCount < ApiConfig.maxRetries) {
            error.requestOptions.extra['retryCount'] = retryCount + 1;

            // Wait before retry with exponential backoff
            final delay = (ApiConfig.retryDelay * (2.0 * retryCount)).round();
            await Future.delayed(Duration(milliseconds: delay));

            try {
              final response = await _dio.fetch(error.requestOptions);
              handler.resolve(response);
              return;
            } catch (retryError) {
              // Continue with original error if retry fails
            }
          }

          handler.next(error);
        },
      ),
    );

    // Detailed logging interceptor (only in debug mode)
    if (ApiConfig.enableLogging) {
      _dio.interceptors.add(
        LogInterceptor(
          requestBody: true,
          responseBody: true,
          requestHeader: false, // Avoid logging sensitive headers
          responseHeader: false,
          error: true,
          logPrint: (obj) {
            Logger.d('🌐 API: $obj');
          },
        ),
      );
    }
  }

  bool _shouldRetry(DioException error) {
    // Retry on network errors and timeouts, but not on client/server errors
    return error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.sendTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        (error.type == DioExceptionType.unknown &&
            error.error.toString().contains('SocketException'));
  }

  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _secureStorage.read(key: 'refresh_token');
      if (refreshToken == null) return false;

      final response = await _dio.post(
        ApiConfig.refresh,
        data: {'refresh_token': refreshToken},
        options: Options(headers: {'Authorization': null}),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        await _secureStorage.write(
          key: 'access_token',
          value: data['access_token'],
        );
        if (data['refresh_token'] != null) {
          await _secureStorage.write(
            key: 'refresh_token',
            value: data['refresh_token'],
          );
        }
        return true;
      }
    } catch (e) {
      Logger.e('Token refresh failed: $e');
      await AuthService().logout(); // Logout user if token refresh fails
    }
    return false;
  }

  Future<bool> isConnected() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  // Generic GET request
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // Generic POST request
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // POST request with form data (for OAuth2 authentication)
  Future<Response<T>> postFormData<T>(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final formData = data != null ? FormData.fromMap(data) : null;
      return await _dio.post<T>(
        path,
        data: formData,
        queryParameters: queryParameters,
        options:
            options?.copyWith(
              contentType: 'application/x-www-form-urlencoded',
            ) ??
            Options(contentType: 'application/x-www-form-urlencoded'),
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // Generic PUT request
  Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.put<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // Generic DELETE request
  Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.delete<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // Authentication implementation
  @override
  Future<AuthResponseDto> login(String username, String password) async {
    try {
      final response = await post(
        ApiConfig.login,
        data: {'username': username, 'password': password},
      );
      return AuthResponseDto.fromJson(response.data);
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  @override
  Future<AuthResponseDto> refreshToken(String refreshToken) async {
    try {
      final response = await post(
        ApiConfig.refresh,
        data: {'refresh_token': refreshToken},
      );
      return AuthResponseDto.fromJson(response.data);
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  @override
  Future<void> logout() async {
    try {
      await post(ApiConfig.logout);
      // Clear stored tokens
      await _secureStorage.delete(key: 'access_token');
      await _secureStorage.delete(key: 'refresh_token');
    } catch (e) {
      // Even if logout fails on server, clear local tokens
      await _secureStorage.delete(key: 'access_token');
      await _secureStorage.delete(key: 'refresh_token');
      throw _handleError(e as DioException);
    }
  }

  // Application CRUD implementation
  @override
  Future<List<ApplicationDto>> getApplications({
    Map<String, dynamic>? filters,
  }) async {
    try {
      final response = await get(
        ApiConfig.getApplications,
        queryParameters: filters,
      );
      final List<dynamic> data = response.data['applications'] ?? response.data;
      return data.map((json) => ApplicationDto.fromJson(json)).toList();
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  @override
  Future<ApplicationDto> getApplication(String id) async {
    try {
      final response = await get('${ApiConfig.applications}/$id');
      return ApplicationDto.fromJson(response.data);
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  @override
  Future<ApplicationDto> createApplication(ApplicationDto application) async {
    try {
      final response = await post(
        ApiConfig.createApplication,
        data: application.toJson(),
      );
      return ApplicationDto.fromJson(response.data);
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  @override
  Future<ApplicationDto> updateApplication(
    String id,
    ApplicationDto application,
  ) async {
    try {
      final response = await put(
        '${ApiConfig.updateApplication}/$id',
        data: application.toJson(),
      );
      return ApplicationDto.fromJson(response.data);
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  @override
  Future<void> deleteApplication(String id) async {
    try {
      await delete('${ApiConfig.applications}/$id');
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  @override
  Future<void> updateApplicationStatus(
    String applicationId,
    String newStatus, {
    String? rejectionReason,
  }) async {
    try {
      final body = {'status': newStatus};
      if (rejectionReason != null) {
        body['rejection_reason'] = rejectionReason;
      }

      await put('${ApiConfig.applications}/$applicationId/status', data: body);
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  // File upload implementation
  @override
  Future<String> uploadFile(
    File file,
    String applicationId,
    String fileType, {
    Map<String, String>? additionalData,
  }) async {
    try {
      final formData = FormData();

      formData.files.add(
        MapEntry(
          'file',
          await MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
          ),
        ),
      );

      formData.fields.add(MapEntry('application_id', applicationId));
      formData.fields.add(MapEntry('file_type', fileType));
      additionalData?.forEach((key, value) {
        formData.fields.add(MapEntry(key, value));
      });

      final response = await _dio.post(
        ApiConfig.uploadFile,
        data: formData,
        options: Options(headers: {'Content-Type': 'multipart/form-data'}),
      );

      return response.data['file_url'] ?? response.data['url'] ?? '';
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  @override
  Future<List<String>> uploadFiles(
    List<File> files,
    String applicationId,
    String fileType,
  ) async {
    try {
      final List<String> urls = [];
      for (final file in files) {
        final url = await uploadFile(
          file,
          applicationId,
          fileType,
          additionalData: {},
        );
        urls.add(url);
      }
      return urls;
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  @override
  Future<String> uploadDocument(
    File file,
    String applicationId,
    String documentType, {
    Map<String, String>? additionalData,
  }) async {
    try {
      final formData = FormData();

      formData.files.add(
        MapEntry(
          'file',
          await MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
          ),
        ),
      );

      formData.fields.add(MapEntry('application_id', applicationId));
      formData.fields.add(MapEntry('document_type', documentType));
      additionalData?.forEach((key, value) {
        formData.fields.add(MapEntry(key, value));
      });

      final response = await _dio.post(
        ApiConfig.uploadFile,
        data: formData,
        options: Options(headers: {'Content-Type': 'multipart/form-data'}),
      );

      return response.data['file_url'] ??
          response.data['url'] ??
          response.data['download_url'] ??
          '';
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  @override
  Future<Uint8List> downloadFile(String fileId) async {
    try {
      final response = await _dio.get(
        '${ApiConfig.getFile}/$fileId/download',
        options: Options(responseType: ResponseType.bytes),
      );

      return response.data;
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  // Sync operations implementation
  @override
  Future<SyncStatusDto> getSyncStatus(String applicationId) async {
    try {
      final response = await get(
        '${ApiConfig.syncApplications}/$applicationId/status',
      );
      return SyncStatusDto.fromJson(response.data);
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  @override
  Future<List<SyncStatusDto>> getBatchSyncStatus(
    List<String> applicationIds,
  ) async {
    try {
      final response = await post(
        '${ApiConfig.syncApplications}/batch-status',
        data: {'application_ids': applicationIds},
      );
      final List<dynamic> data = response.data['statuses'] ?? response.data;
      return data.map((json) => SyncStatusDto.fromJson(json)).toList();
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  @override
  Future<void> markAsSynced(String applicationId) async {
    try {
      await post('${ApiConfig.syncApplications}/$applicationId/mark-synced');
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  // Batch operations implementation
  @override
  Future<List<ApplicationDto>> createApplicationsBatch(
    List<ApplicationDto> applications,
  ) async {
    try {
      final response = await post(
        '${ApiConfig.applications}/batch',
        data: {
          'applications': applications.map((app) => app.toJson()).toList(),
        },
      );
      final List<dynamic> data = response.data['applications'] ?? response.data;
      return data.map((json) => ApplicationDto.fromJson(json)).toList();
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  @override
  Future<List<ApplicationDto>> updateApplicationsBatch(
    List<ApplicationDto> applications,
  ) async {
    try {
      final response = await put(
        '${ApiConfig.applications}/batch',
        data: {
          'applications': applications.map((app) => app.toJson()).toList(),
        },
      );
      final List<dynamic> data = response.data['applications'] ?? response.data;
      return data.map((json) => ApplicationDto.fromJson(json)).toList();
    } catch (e) {
      throw _handleError(e as DioException);
    }
  }

  // Dashboard implementation
  @override
  Future<DashboardStatsDto> getDashboardStats({
    Map<String, dynamic>? filters,
  }) async {
    try {
      final response = await get(
        ApiConfig.dashboardStats,
        queryParameters: filters,
      );
      return DashboardStatsDto.fromJson(response.data);
    } catch (e) {
      Logger.e('Failed to get dashboard stats: $e');
      // Return empty stats as fallback
      return DashboardStatsDto.empty();
    }
  }

  @override
  Future<List<ApplicationDto>> getRecentApplications({int? limit}) async {
    try {
      Logger.d('Attempting to get recent applications from server...');
      final response = await get(
        ApiConfig.dashboardRecentApplications,
        queryParameters: limit != null ? {'limit': limit} : null,
      );

      Logger.d('Successfully retrieved recent applications.');

      // The recent applications endpoint returns a simplified format
      final List<dynamic> data =
          response.data is List
              ? response.data
              : (response.data['applications'] ?? response.data);

      return data.map((json) {
        try {
          // For recent applications, create ApplicationDto directly to avoid fromJson issues
          return ApplicationDto(
            id: json['id']?.toString(),
            status: json['status'] ?? 'pending',
            fullNameLatin: json['full_name_latin'],
            fullNameKhmer: json['full_name_khmer'],
            requestedAmount: json['requested_amount']?.toDouble(),
            createdAt: json['created_at'],
            syncStatus: 'synced',
            version: 1,
          );
        } catch (e) {
          Logger.e('Error converting recent application: $e');
          Logger.e('Raw data: $json');
          // Return a minimal ApplicationDto for failed conversions
          return ApplicationDto(
            id: json['id']?.toString() ?? '',
            status: json['status'] ?? 'pending',
            fullNameLatin: json['full_name_latin'],
            requestedAmount: json['requested_amount']?.toDouble(),
            createdAt: json['created_at'],
          );
        }
      }).toList();
    } catch (e) {
      Logger.e('Error getting recent applications from server: $e');
      return [];
    }
  }

  // Health check implementation
  @override
  Future<bool> isServerHealthy() async {
    try {
      final response = await get('/health');
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  ApiException _handleError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ApiException(
          'Connection timeout. Please check your internet connection.',
          type: ApiExceptionType.timeout,
        );
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message =
            error.response?.data?['message'] ?? 'Unknown error occurred';

        if (statusCode == 401) {
          return ApiException(
            'Authentication failed. Please login again.',
            type: ApiExceptionType.unauthorized,
            statusCode: statusCode,
          );
        } else if (statusCode == 403) {
          return ApiException(
            'Access denied. You don\'t have permission to perform this action.',
            type: ApiExceptionType.forbidden,
            statusCode: statusCode,
          );
        } else if (statusCode == 404) {
          return ApiException(
            'Resource not found.',
            type: ApiExceptionType.notFound,
            statusCode: statusCode,
          );
        } else if (statusCode == 422) {
          return ApiException(
            'Validation error: $message',
            type: ApiExceptionType.validation,
            statusCode: statusCode,
            validationErrors: error.response?.data?['errors'],
          );
        } else if (statusCode != null && statusCode >= 500) {
          return ApiException(
            'Server error. Please try again later.',
            type: ApiExceptionType.server,
            statusCode: statusCode,
          );
        }

        return ApiException(
          message,
          type: ApiExceptionType.unknown,
          statusCode: statusCode,
        );
      case DioExceptionType.cancel:
        return ApiException(
          'Request was cancelled.',
          type: ApiExceptionType.cancelled,
        );
      case DioExceptionType.unknown:
      default:
        return ApiException(
          'Network error. Please check your internet connection.',
          type: ApiExceptionType.network,
        );
    }
  }
}

enum ApiExceptionType {
  network,
  timeout,
  unauthorized,
  forbidden,
  notFound,
  validation,
  server,
  cancelled,
  unknown,
}

class ApiException implements Exception {
  final String message;
  final ApiExceptionType type;
  final int? statusCode;
  final Map<String, dynamic>? validationErrors;

  ApiException(
    this.message, {
    required this.type,
    this.statusCode,
    this.validationErrors,
  });

  @override
  String toString() => message;
}
