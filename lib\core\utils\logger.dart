import 'package:flutter/foundation.dart';

/// Logger utility for consistent logging across the application
class Logger {
  static const String _tag = 'LC-WorkFlow';

  /// Log debug message (only in debug mode)
  static void d(String message, {String? tag}) {
    if (kDebugMode) {
      print('${tag ?? _tag} 💬 DEBUG: $message');
    }
  }

  /// Log info message
  static void i(String message, {String? tag}) {
    if (kDebugMode) {
      print('${tag ?? _tag} ℹ️ INFO: $message');
    }
  }

  /// Log warning message
  static void w(String message, {String? tag}) {
    if (kDebugMode) {
      print('${tag ?? _tag} ⚠️ WARN: $message');
    }
  }

  /// Log error message
  static void e(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (kDebugMode) {
      print('${tag ?? _tag} 🔴 ERROR: $message');
      if (error != null) {
        print('${tag ?? _tag} Error details: $error');
      }
      if (stackTrace != null) {
        print('${tag ?? _tag} Stack trace: $stackTrace');
      }
    }
  }

  /// Log API request
  static void logRequest(
    String method,
    String url, {
    Map<String, dynamic>? headers,
    dynamic body,
  }) {
    if (kDebugMode) {
      print('$_tag 🌐 REQUEST: $method $url');
      if (headers != null) {
        print('$_tag Headers: ${_sanitizeHeaders(headers)}');
      }
      if (body != null) {
        print('$_tag Body: $body');
      }
    }
  }

  /// Log API response
  static void logResponse(
    String method,
    String url,
    int statusCode,
    dynamic body,
  ) {
    if (kDebugMode) {
      final emoji = statusCode >= 200 && statusCode < 300 ? '✅' : '❌';
      print('$_tag $emoji RESPONSE: $method $url ($statusCode)');
      print('$_tag Body: $body');
    }
  }

  /// Sanitize headers to remove sensitive information
  static Map<String, dynamic> _sanitizeHeaders(Map<String, dynamic> headers) {
    final sanitized = Map<String, dynamic>.from(headers);
    if (sanitized.containsKey('Authorization')) {
      sanitized['Authorization'] = 'Bearer [REDACTED]';
    }
    return sanitized;
  }
}
