import 'dart:io';
import 'dart:typed_data';
import 'package:lc_work_flow/models/dto/application_dto.dart';
import 'package:lc_work_flow/models/dto/auth_response_dto.dart';
import 'package:lc_work_flow/models/dto/sync_status_dto.dart';

/// Interface for API service operations
abstract class IApiService {
  // Authentication
  Future<AuthResponseDto> login(String username, String password);
  Future<AuthResponseDto> refreshToken(String refreshToken);
  Future<void> logout();

  // Application CRUD operations
  Future<List<ApplicationDto>> getApplications({Map<String, dynamic>? filters});
  Future<ApplicationDto> getApplication(String id);
  Future<ApplicationDto> createApplication(ApplicationDto application);
  Future<ApplicationDto> updateApplication(
    String id,
    ApplicationDto application,
  );
  Future<void> deleteApplication(String id);
  Future<void> updateApplicationStatus(
    String applicationId,
    String newStatus,
    {String? rejectionReason}
  );

  // File upload operations
  Future<String> uploadFile(
    File file,
    String applicationId,
    String fileType, {
    Map<String, String>? additionalData,
  });
  Future<List<String>> uploadFiles(
    List<File> files,
    String applicationId,
    String fileType,
  );
  Future<String> uploadDocument(
    File file,
    String applicationId,
    String documentType, {
    Map<String, String>? additionalData,
  });
  Future<Uint8List> downloadFile(String fileId);

  // Sync operations
  Future<SyncStatusDto> getSyncStatus(String applicationId);
  Future<List<SyncStatusDto>> getBatchSyncStatus(List<String> applicationIds);
  Future<void> markAsSynced(String applicationId);

  // Batch operations for efficiency
  Future<List<ApplicationDto>> createApplicationsBatch(
    List<ApplicationDto> applications,
  );
  Future<List<ApplicationDto>> updateApplicationsBatch(
    List<ApplicationDto> applications,
  );

  // Health check
  Future<bool> isServerHealthy();
}
