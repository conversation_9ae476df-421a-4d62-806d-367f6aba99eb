import 'package:get/get.dart';
import '../core/utils/logger.dart';
import '../models/customer_model.dart';
import '../models/product_type.dart';
import '../services/local_storage_service.dart';

class CustomerController extends GetxController {
  // Use .obs to make the list reactive
  var allCustomers = <Customer>[].obs;
  final LocalStorageService _localStorageService = LocalStorageService();

  @override
  void onInit() {
    super.onInit();
    // Load initial mock data
    _loadMockCustomers();
    // Load customers from local storage
    loadCustomersFromStorage();
  }

  void _loadMockCustomers() {
    allCustomers.assignAll([
      Customer(
        // Basic Info
        id: '1',
        name: '<PERSON><PERSON> <PERSON>',
        phone: '+855 12 345 678',
        nid: '123456789012',
        loanAmount: 2500,
        interestRate: 1.5,
        loanStatus: LoanStatus.disbursed,
        loanPurpose: 'Small Business',
        loanStartDate: DateTime.now().subtract(const Duration(days: 15)),
        loanEndDate: DateTime.now().add(const Duration(days: 345)),

        // Detailed Borrower Information
        idCardType: IdCardType.cambodianIdentity,
        fullNameKhmer: 'ចាន សុខា',
        fullNameLatin: 'Chan Sokha',
        dateOfBirth: DateTime(1985, 3, 15),
        idNumber: '123456789012',
        portfolioOfficerName: 'Chen Sopheakdey',

        // Loan Details
        requestedAmount: 2500.0,
        loanPurposes: [LoanPurposeType.commerce],
        purposeDetails: 'Expand grocery store inventory',
        productType: ProductType.monthly,
        desiredLoanTerm: '12 months',
        requestedDisbursementDate: DateTime.now().subtract(
          const Duration(days: 15),
        ),

        // Guarantor Information
        guarantorName: 'Kim Dara',
        guarantorPhone: '+855 98 765 432',

        // Photo paths (mock paths for demo)
        profileImage: 'https://i.pravatar.cc/150?img=1',
        borrowerNidPhotoPath: 'assets/demo/sokha_nid.jpg',
        borrowerHomeOrLandPhotoPath: 'assets/demo/sokha_home.jpg',
        borrowerBusinessPhotoPath: 'assets/demo/sokha_business.jpg',
        guarantorNidPhotoPath: 'assets/demo/guarantor_nid.jpg',
        guarantorHomeOrLandPhotoPath: 'assets/demo/guarantor_home.jpg',
        guarantorBusinessPhotoPath: 'assets/demo/guarantor_business.jpg',
        profilePhotoPath: 'assets/demo/sokha_profile.jpg',
      ),
      Customer(
        // Basic Info
        id: '2',
        name: 'Dara Kim',
        phone: '+855 23 456 789',
        nid: '987654321098',
        loanAmount: 5000,
        interestRate: 1.8,
        loanStatus: LoanStatus.approved,
        loanPurpose: 'Education',
        loanStartDate: DateTime.now().add(const Duration(days: 2)),
        loanEndDate: DateTime.now().add(const Duration(days: 367)),

        // Detailed Borrower Information
        idCardType: IdCardType.nid,
        fullNameKhmer: 'គីម ដារ៉ា',
        fullNameLatin: 'Kim Dara',
        dateOfBirth: DateTime(1990, 7, 22),
        idNumber: '987654321098',
        portfolioOfficerName: 'Chen Sopheakdey',

        // Loan Details
        requestedAmount: 5000.0,
        loanPurposes: [LoanPurposeType.family],
        purposeDetails: 'University tuition fees for daughter',
        productType: ProductType.monthly,
        desiredLoanTerm: '24 months',
        requestedDisbursementDate: DateTime.now().add(const Duration(days: 2)),

        // Guarantor Information
        guarantorName: 'Lim Sopheap',
        guarantorPhone: '+855 77 888 999',

        // Photo paths (mock paths for demo)
        profileImage: 'https://i.pravatar.cc/150?img=2',
        borrowerNidPhotoPath: 'assets/demo/dara_nid.jpg',
        borrowerHomeOrLandPhotoPath: 'assets/demo/dara_home.jpg',
        borrowerBusinessPhotoPath: 'assets/demo/dara_business.jpg',
        guarantorNidPhotoPath: 'assets/demo/guarantor2_nid.jpg',
        guarantorHomeOrLandPhotoPath: 'assets/demo/guarantor2_home.jpg',
        profilePhotoPath: 'assets/demo/dara_profile.jpg',
      ),
      Customer(
        // Basic Info
        id: '3',
        name: 'Sopheap Lim',
        phone: '+855 34 567 890',
        nid: '456789123456',
        loanAmount: 1000,
        interestRate: 2.0,
        loanStatus: LoanStatus.pending,
        loanPurpose: 'Emergency',
        loanStartDate: DateTime.now(),
        loanEndDate: DateTime.now().add(const Duration(days: 180)),

        // Detailed Borrower Information
        idCardType: IdCardType.passport,
        fullNameKhmer: 'លីម សុភាព',
        fullNameLatin: 'Lim Sopheap',
        dateOfBirth: DateTime(1988, 11, 8),
        idNumber: '456789123456',
        portfolioOfficerName: 'Chen Sopheakdey',

        // Loan Details
        requestedAmount: 1000.0,
        loanPurposes: [LoanPurposeType.family],
        purposeDetails: 'Medical emergency expenses',
        productType: ProductType.weekly,
        desiredLoanTerm: '6 months',
        requestedDisbursementDate: DateTime.now(),

        // No guarantor for this loan

        // Photo paths (mock paths for demo)
        profileImage: 'https://i.pravatar.cc/150?img=3',
        borrowerNidPhotoPath: 'assets/demo/sopheap_nid.jpg',
        borrowerHomeOrLandPhotoPath: 'assets/demo/sopheap_home.jpg',
        profilePhotoPath: 'assets/demo/sopheap_profile.jpg',
      ),
      Customer(
        // Basic Info
        id: '4',
        name: 'Rithy Chhun',
        phone: '+855 45 678 901',
        nid: '789123456789',
        loanAmount: 7500,
        interestRate: 1.2,
        loanStatus: LoanStatus.completed,
        loanPurpose: 'Agriculture',
        loanStartDate: DateTime.now().subtract(const Duration(days: 60)),
        loanEndDate: DateTime.now().subtract(const Duration(days: 30)),

        // Detailed Borrower Information
        idCardType: IdCardType.cambodianIdentity,
        fullNameKhmer: 'ឈុន រិទ្ធី',
        fullNameLatin: 'Chhun Rithy',
        dateOfBirth: DateTime(1975, 5, 12),
        idNumber: '789123456789',
        portfolioOfficerName: 'Chen Sopheakdey',

        // Loan Details
        requestedAmount: 7500.0,
        loanPurposes: [LoanPurposeType.agriculture],
        purposeDetails: 'Purchase rice farming equipment',
        productType: ProductType.biweekly,
        desiredLoanTerm: '18 months',
        requestedDisbursementDate: DateTime.now().subtract(
          const Duration(days: 60),
        ),

        // Guarantor Information
        guarantorName: 'Oum Sreyneang',
        guarantorPhone: '+855 66 777 888',

        // Photo paths (mock paths for demo)
        profileImage: 'https://i.pravatar.cc/150?img=4',
        borrowerNidPhotoPath: 'assets/demo/rithy_nid.jpg',
        borrowerHomeOrLandPhotoPath: 'assets/demo/rithy_farm.jpg',
        borrowerBusinessPhotoPath: 'assets/demo/rithy_equipment.jpg',
        guarantorNidPhotoPath: 'assets/demo/guarantor3_nid.jpg',
        guarantorHomeOrLandPhotoPath: 'assets/demo/guarantor3_home.jpg',
        profilePhotoPath: 'assets/demo/rithy_profile.jpg',
      ),
      Customer(
        // Basic Info
        id: '5',
        name: 'Sreyneang Oum',
        phone: '+855 56 789 012',
        nid: '321654987321',
        loanAmount: 3000,
        interestRate: 2.5,
        loanStatus: LoanStatus.rejected,
        loanPurpose: 'Home Renovation',
        loanStartDate: DateTime.now().subtract(const Duration(days: 5)),
        loanEndDate: DateTime.now().add(const Duration(days: 355)),

        // Detailed Borrower Information
        idCardType: IdCardType.driverLicense,
        fullNameKhmer: 'អ៊ុំ ស្រីនាង',
        fullNameLatin: 'Oum Sreyneang',
        dateOfBirth: DateTime(1992, 9, 25),
        idNumber: '321654987321',
        portfolioOfficerName: 'Chen Sopheakdey',

        // Loan Details
        requestedAmount: 3000.0,
        loanPurposes: [LoanPurposeType.construction],
        purposeDetails: 'Kitchen and bathroom renovation',
        productType: ProductType.daily,
        desiredLoanTerm: '12 months',
        requestedDisbursementDate: DateTime.now().subtract(
          const Duration(days: 5),
        ),

        // Guarantor Information
        guarantorName: 'Chan Sokha',
        guarantorPhone: '+855 12 345 678',

        // Photo paths (mock paths for demo)
        profileImage: 'https://i.pravatar.cc/150?img=5',
        borrowerNidPhotoPath: 'assets/demo/sreyneang_nid.jpg',
        borrowerHomeOrLandPhotoPath: 'assets/demo/sreyneang_home.jpg',
        guarantorNidPhotoPath: 'assets/demo/guarantor4_nid.jpg',
        guarantorHomeOrLandPhotoPath: 'assets/demo/guarantor4_home.jpg',
        profilePhotoPath: 'assets/demo/sreyneang_profile.jpg',
      ),
    ]);

    // Sort customers by creation date (newest first)
    sortCustomersByDate();
  }

  Future<void> addCustomer(Customer customer) async {
    // Add customer to the local list
    allCustomers.add(customer);

    // Save to local storage
    try {
      await _localStorageService.saveDraftApplication(
        idCardType: customer.idCardType?.toString(),
        idNumber: customer.idNumber,
        fullNameKhmer: customer.fullNameKhmer,
        fullNameLatin: customer.fullNameLatin,
        phone: customer.phone,
        dateOfBirth: customer.dateOfBirth,
        portfolioOfficerName: customer.portfolioOfficerName,
        requestedAmount: customer.requestedAmount,
        loanPurposes: customer.loanPurposes?.map((e) => e.toString()).toList(),
        purposeDetails: customer.purposeDetails,
        productType: customer.productType?.toString(),
        desiredLoanTerm: customer.desiredLoanTerm,
        requestedDisbursementDate: customer.requestedDisbursementDate,
        guarantorName: customer.guarantorName,
        guarantorPhone: customer.guarantorPhone,
        idCardImages: [],
        borrowerNidPhoto: customer.borrowerNidPhotoPath,
        borrowerHomePhoto: customer.borrowerHomeOrLandPhotoPath,
        borrowerBusinessPhoto: customer.borrowerBusinessPhotoPath,
        guarantorNidPhoto: customer.guarantorNidPhotoPath,
        guarantorHomePhoto: customer.guarantorHomeOrLandPhotoPath,
        guarantorBusinessPhoto: customer.guarantorBusinessPhotoPath,
        profilePhoto: customer.profilePhotoPath,
        selectedCollateralTypes: [],
        collaterals: customer.collaterals,
        documents: customer.documents,
      );

      // Refresh the list from storage
      await loadCustomersFromStorage();
    } catch (e) {
      Logger.e('Error saving customer: $e');
    }
  }

  // Sort customers by creation date (newest first)
  void sortCustomersByDate() {
    allCustomers.sort((a, b) => b.loanStartDate.compareTo(a.loanStartDate));
  }

  // Load customers from local storage and merge with existing list
  Future<void> loadCustomersFromStorage() async {
    try {
      // Get both draft and submitted applications
      final draftApplications =
          await _localStorageService.getDraftApplications();
      final submittedApplications =
          await _localStorageService.getSubmittedApplications();

      // Convert to Customer objects
      final draftCustomers =
          draftApplications
              .map((data) => _localStorageService.convertToCustomer(data))
              .toList();
      final submittedCustomers =
          submittedApplications
              .map((data) => _localStorageService.convertToCustomer(data))
              .toList();

      // Combine all customers from storage
      final storageCustomers = [...draftCustomers, ...submittedCustomers];

      // Remove any existing customers that might be duplicates (by ID)
      final existingIds = allCustomers.map((c) => c.id).toSet();
      final newCustomers =
          storageCustomers
              .where((customer) => !existingIds.contains(customer.id))
              .toList();

      // Add new customers to the list
      allCustomers.addAll(newCustomers);

      // Sort the entire list by creation date
      sortCustomersByDate();
    } catch (e) {
      Logger.e('Error loading customers from storage: $e');
    }
  }

  // Refresh the customer list (reload from storage)
  Future<void> refreshCustomers() async {
    await loadCustomersFromStorage();
  }
}
