import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:lc_work_flow/core/config/api_config.dart';
import 'package:lc_work_flow/core/utils/logger.dart';

/// Factory class to create and configure Dio HTTP client instances
class DioClientFactory {
  /// Create a new Dio instance with default configuration
  static Dio createDio({
    FlutterSecureStorage? secureStorage,
    bool? enableLogging,
  }) {
    final bool shouldEnableLogging = enableLogging ?? ApiConfig.enableLogging;
    final dio = Dio(
      BaseOptions(
        baseUrl: ApiConfig.baseUrl,
        connectTimeout: Duration(milliseconds: ApiConfig.connectTimeout),
        receiveTimeout: Duration(milliseconds: ApiConfig.receiveTimeout),
        sendTimeout: Duration(milliseconds: ApiConfig.sendTimeout),
        headers: ApiConfig.defaultHeaders,
        validateStatus: (status) {
          // Accept status codes 200-299 and some 4xx for proper error handling
          return status != null && status < 500;
        },
      ),
    );

    // Add interceptors
    _addInterceptors(dio, secureStorage, shouldEnableLogging);

    return dio;
  }

  /// Add standard interceptors to Dio instance
  static void _addInterceptors(
    Dio dio,
    FlutterSecureStorage? secureStorage,
    bool enableLogging,
  ) {
    // Auth interceptor - Add auth token and handle token refresh
    dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Skip auth for login and refresh endpoints
          if (!options.path.contains('/auth/token') &&
              !options.path.contains('/auth/refresh')) {
            final storage = secureStorage ?? const FlutterSecureStorage();
            final token = await storage.read(key: 'access_token');
            if (token != null) {
              options.headers['Authorization'] = 'Bearer $token';
            }
          }

          // Add request timestamp for debugging
          if (enableLogging) {
            options.headers['X-Request-Time'] =
                DateTime.now().toIso8601String();
          }

          handler.next(options);
        },
        onResponse: (response, handler) async {
          // Log successful responses in debug mode
          if (enableLogging) {
            Logger.logResponse(
              response.requestOptions.method,
              response.requestOptions.path,
              response.statusCode ?? 0,
              response.data,
            );
          }
          handler.next(response);
        },
        onError: (error, handler) async {
          // Log errors in debug mode
          if (enableLogging) {
            Logger.e(
              '${error.requestOptions.method} ${error.requestOptions.path} - ${error.response?.statusCode ?? 'Network Error'}',
              error: error,
            );
            if (error.response?.data != null) {
              Logger.e('Error data: ${error.response?.data}');
            }
          }
          handler.next(error);
        },
      ),
    );

    // Retry interceptor for network failures
    dio.interceptors.add(
      InterceptorsWrapper(
        onError: (error, handler) async {
          if (_shouldRetry(error) &&
              error.requestOptions.extra['retryCount'] == null) {
            error.requestOptions.extra['retryCount'] = 0;
          }

          final retryCount = error.requestOptions.extra['retryCount'] ?? 0;
          if (_shouldRetry(error) && retryCount < ApiConfig.maxRetries) {
            error.requestOptions.extra['retryCount'] = retryCount + 1;

            // Wait before retry with exponential backoff
            final delay =
                (ApiConfig.retryDelay *
                        (ApiConfig.retryBackoffMultiplier * retryCount))
                    .round();
            await Future.delayed(Duration(milliseconds: delay));

            try {
              final response = await dio.fetch(error.requestOptions);
              handler.resolve(response);
              return;
            } catch (retryError) {
              // Continue with original error if retry fails
            }
          }

          handler.next(error);
        },
      ),
    );

    // Detailed logging interceptor (only in debug mode)
    if (enableLogging) {
      dio.interceptors.add(
        LogInterceptor(
          requestBody: true,
          responseBody: true,
          requestHeader: false, // Avoid logging sensitive headers
          responseHeader: false,
          error: true,
          logPrint: (obj) {
            Logger.d('API: $obj');
          },
        ),
      );
    }
  }

  /// Determine if a request should be retried based on error type
  static bool _shouldRetry(DioException error) {
    // Retry on network errors and timeouts, but not on client/server errors
    return error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.sendTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        (error.type == DioExceptionType.unknown &&
            error.error.toString().contains('SocketException'));
  }
}
