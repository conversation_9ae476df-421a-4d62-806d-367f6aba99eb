import 'package:equatable/equatable.dart';

/// Data Transfer Object for Application API communication
class ApplicationDto extends Equatable {
  final String? id;
  final String status;
  final String? idCardType;
  final String? idNumber;
  final String? fullNameKhmer;
  final String? fullNameLatin;
  final String? phone;
  final String? dateOfBirth;
  final String? portfolioOfficerName;
  final double? requestedAmount;
  final List<String>? loanPurposes;
  final String? purposeDetails;
  final String? productType;
  final String? desiredLoanTerm;
  final String? requestedDisbursementDate;
  final String? guarantorName;
  final String? guarantorPhone;
  final List<String>? idCardImages;
  final String? borrowerNidPhoto;
  final String? borrowerHomePhoto;
  final String? borrowerBusinessPhoto;
  final String? guarantorNidPhoto;
  final String? guarantorHomePhoto;
  final String? guarantorBusinessPhoto;
  final String? profilePhoto;
  final List<String>? selectedCollateralTypes;
  final List<Map<String, dynamic>>? collaterals;
  final List<Map<String, dynamic>>? documents;
  final String? createdAt;
  final String? updatedAt;
  final String? syncStatus;
  final String? lastSyncedAt;
  final int? version;

  const ApplicationDto({
    this.id,
    required this.status,
    this.idCardType,
    this.idNumber,
    this.fullNameKhmer,
    this.fullNameLatin,
    this.phone,
    this.dateOfBirth,
    this.portfolioOfficerName,
    this.requestedAmount,
    this.loanPurposes,
    this.purposeDetails,
    this.productType,
    this.desiredLoanTerm,
    this.requestedDisbursementDate,
    this.guarantorName,
    this.guarantorPhone,
    this.idCardImages,
    this.borrowerNidPhoto,
    this.borrowerHomePhoto,
    this.borrowerBusinessPhoto,
    this.guarantorNidPhoto,
    this.guarantorHomePhoto,
    this.guarantorBusinessPhoto,
    this.profilePhoto,
    this.selectedCollateralTypes,
    this.collaterals,
    this.documents,
    this.createdAt,
    this.updatedAt,
    this.syncStatus,
    this.lastSyncedAt,
    this.version,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'status': status,
      'id_card_type': idCardType,
      'id_number': idNumber,
      'full_name_khmer': fullNameKhmer,
      'full_name_latin': fullNameLatin,
      'phone': phone,
      'date_of_birth': dateOfBirth,
      'portfolio_officer_name': portfolioOfficerName,
      'requested_amount': requestedAmount,
      'loan_purposes': loanPurposes,
      'purpose_details': purposeDetails,
      'product_type': productType,
      'desired_loan_term': desiredLoanTerm,
      'requested_disbursement_date': requestedDisbursementDate,
      'guarantor_name': guarantorName,
      'guarantor_phone': guarantorPhone,
      'id_card_images': idCardImages,
      'borrower_nid_photo': borrowerNidPhoto,
      'borrower_home_photo': borrowerHomePhoto,
      'borrower_business_photo': borrowerBusinessPhoto,
      'guarantor_nid_photo': guarantorNidPhoto,
      'guarantor_home_photo': guarantorHomePhoto,
      'guarantor_business_photo': guarantorBusinessPhoto,
      'profile_photo': profilePhoto,
      'selected_collateral_types': selectedCollateralTypes,
      'collaterals': collaterals,
      'documents': documents,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'sync_status': syncStatus,
      'last_synced_at': lastSyncedAt,
      'version': version,
    };
  }

  factory ApplicationDto.fromJson(Map<String, dynamic> json) {
    return ApplicationDto(
      id: json['id'],
      status: json['status'] ?? 'draft',
      idCardType: json['id_card_type'],
      idNumber: json['id_number'],
      fullNameKhmer: json['full_name_khmer'],
      fullNameLatin: json['full_name_latin'],
      phone: json['phone'],
      dateOfBirth: json['date_of_birth'],
      portfolioOfficerName: json['portfolio_officer_name'],
      requestedAmount: json['requested_amount']?.toDouble(),
      loanPurposes:
          json['loan_purposes'] != null
              ? List<String>.from(json['loan_purposes'])
              : null,
      purposeDetails: json['purpose_details'],
      productType: json['product_type'],
      desiredLoanTerm: json['desired_loan_term'],
      requestedDisbursementDate: json['requested_disbursement_date'],
      guarantorName: json['guarantor_name'],
      guarantorPhone: json['guarantor_phone'],
      idCardImages:
          json['id_card_images'] != null
              ? List<String>.from(json['id_card_images'])
              : null,
      borrowerNidPhoto: json['borrower_nid_photo'],
      borrowerHomePhoto: json['borrower_home_photo'],
      borrowerBusinessPhoto: json['borrower_business_photo'],
      guarantorNidPhoto: json['guarantor_nid_photo'],
      guarantorHomePhoto: json['guarantor_home_photo'],
      guarantorBusinessPhoto: json['guarantor_business_photo'],
      profilePhoto: json['profile_photo'],
      selectedCollateralTypes:
          json['selected_collateral_types'] != null
              ? List<String>.from(json['selected_collateral_types'])
              : null,
      collaterals:
          json['collaterals'] != null
              ? List<Map<String, dynamic>>.from(json['collaterals'])
              : null,
      documents:
          json['documents'] != null
              ? List<Map<String, dynamic>>.from(json['documents'])
              : null,
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      syncStatus: json['sync_status'],
      lastSyncedAt: json['last_synced_at'],
      version: json['version'],
    );
  }

  ApplicationDto copyWith({
    String? id,
    String? status,
    String? idCardType,
    String? idNumber,
    String? fullNameKhmer,
    String? fullNameLatin,
    String? phone,
    String? dateOfBirth,
    String? portfolioOfficerName,
    double? requestedAmount,
    List<String>? loanPurposes,
    String? purposeDetails,
    String? productType,
    String? desiredLoanTerm,
    String? requestedDisbursementDate,
    String? guarantorName,
    String? guarantorPhone,
    List<String>? idCardImages,
    String? borrowerNidPhoto,
    String? borrowerHomePhoto,
    String? borrowerBusinessPhoto,
    String? guarantorNidPhoto,
    String? guarantorHomePhoto,
    String? guarantorBusinessPhoto,
    String? profilePhoto,
    List<String>? selectedCollateralTypes,
    List<Map<String, dynamic>>? collaterals,
    List<Map<String, dynamic>>? documents,
    String? createdAt,
    String? updatedAt,
    String? syncStatus,
    String? lastSyncedAt,
    int? version,
  }) {
    return ApplicationDto(
      id: id ?? this.id,
      status: status ?? this.status,
      idCardType: idCardType ?? this.idCardType,
      idNumber: idNumber ?? this.idNumber,
      fullNameKhmer: fullNameKhmer ?? this.fullNameKhmer,
      fullNameLatin: fullNameLatin ?? this.fullNameLatin,
      phone: phone ?? this.phone,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      portfolioOfficerName: portfolioOfficerName ?? this.portfolioOfficerName,
      requestedAmount: requestedAmount ?? this.requestedAmount,
      loanPurposes: loanPurposes ?? this.loanPurposes,
      purposeDetails: purposeDetails ?? this.purposeDetails,
      productType: productType ?? this.productType,
      desiredLoanTerm: desiredLoanTerm ?? this.desiredLoanTerm,
      requestedDisbursementDate:
          requestedDisbursementDate ?? this.requestedDisbursementDate,
      guarantorName: guarantorName ?? this.guarantorName,
      guarantorPhone: guarantorPhone ?? this.guarantorPhone,
      idCardImages: idCardImages ?? this.idCardImages,
      borrowerNidPhoto: borrowerNidPhoto ?? this.borrowerNidPhoto,
      borrowerHomePhoto: borrowerHomePhoto ?? this.borrowerHomePhoto,
      borrowerBusinessPhoto:
          borrowerBusinessPhoto ?? this.borrowerBusinessPhoto,
      guarantorNidPhoto: guarantorNidPhoto ?? this.guarantorNidPhoto,
      guarantorHomePhoto: guarantorHomePhoto ?? this.guarantorHomePhoto,
      guarantorBusinessPhoto:
          guarantorBusinessPhoto ?? this.guarantorBusinessPhoto,
      profilePhoto: profilePhoto ?? this.profilePhoto,
      selectedCollateralTypes:
          selectedCollateralTypes ?? this.selectedCollateralTypes,
      collaterals: collaterals ?? this.collaterals,
      documents: documents ?? this.documents,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncStatus: syncStatus ?? this.syncStatus,
      lastSyncedAt: lastSyncedAt ?? this.lastSyncedAt,
      version: version ?? this.version,
    );
  }

  @override
  List<Object?> get props => [
    id,
    status,
    idCardType,
    idNumber,
    fullNameKhmer,
    fullNameLatin,
    phone,
    dateOfBirth,
    portfolioOfficerName,
    requestedAmount,
    loanPurposes,
    purposeDetails,
    productType,
    desiredLoanTerm,
    requestedDisbursementDate,
    guarantorName,
    guarantorPhone,
    idCardImages,
    borrowerNidPhoto,
    borrowerHomePhoto,
    borrowerBusinessPhoto,
    guarantorNidPhoto,
    guarantorHomePhoto,
    guarantorBusinessPhoto,
    profilePhoto,
    selectedCollateralTypes,
    collaterals,
    documents,
    createdAt,
    updatedAt,
    syncStatus,
    lastSyncedAt,
    version,
  ];
}
