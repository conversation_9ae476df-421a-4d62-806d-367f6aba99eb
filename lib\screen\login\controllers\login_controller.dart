import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lc_work_flow/core/routes/app_pages.dart';
import 'package:lc_work_flow/services/auth_service.dart';

class LoginController extends GetxController {
  final usernameController = TextEditingController();
  final passwordController = TextEditingController();
  final isObscure = true.obs;
  final isLoading = false.obs;

  final AuthService _authService = AuthService();

  @override
  void onClose() {
    usernameController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  @override
  void onInit() {
    super.onInit();
    _checkExistingLogin();
  }

  Future<void> _checkExistingLogin() async {
    if (await _authService.isLoggedIn()) {
      Get.offNamed(Routes.dashboard);
    }
  }

  Future<void> login() async {
    String username = usernameController.text.trim();
    String password = passwordController.text;

    // Basic Validation
    if (username.isEmpty || password.isEmpty) {
      Get.snackbar(
        'Error',
        'Please fill in all fields',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(
          alpha: (255 * 0.8).roundToDouble(),
          red: Colors.red.r,
          green: Colors.red.g,
          blue: Colors.red.b,
        ),
        colorText: Colors.white,
      );
      return;
    }

    isLoading.value = true;

    try {
      final result = await _authService.login(username, password);

      if (result.isSuccess) {
        Get.snackbar(
          'Success',
          'Welcome back, ${result.user?.displayName ?? username}!',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withValues(
            alpha: (255 * 0.8).roundToDouble(),
            red: Colors.green.r,
            green: Colors.green.g,
            blue: Colors.green.b,
          ),
          colorText: Colors.white,
        );

        // Navigate to dashboard
        Get.offNamed(Routes.dashboard);
      } else {
        Get.snackbar(
          'Login Failed',
          result.error ?? 'Invalid username or password',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.withValues(
            alpha: (255 * 0.8).roundToDouble(),
            red: Colors.red.r,
            green: Colors.red.g,
            blue: Colors.red.b,
          ),
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Login failed. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withValues(
          alpha: (255 * 0.8).roundToDouble(),
          red: Colors.red.r,
          green: Colors.red.g,
          blue: Colors.red.b,
        ),
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> logout() async {
    isLoading.value = true;

    try {
      await _authService.logout();
      Get.offAllNamed(Routes.login);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Logout failed. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Demo login for testing (remove in production)
  Future<void> demoLogin() async {
    usernameController.text = '<EMAIL>';
    passwordController.text = 'demo123';
    await login();
  }
}
