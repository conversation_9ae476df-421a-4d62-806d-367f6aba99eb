import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';
import 'package:lc_work_flow/core/utils/logger.dart';
import 'package:lc_work_flow/repositories/application_repository.dart';

class ConnectivityService extends GetxService {
  static ConnectivityService get to => Get.find();

  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<ConnectivityResult> _connectivitySubscription;

  final _isConnected = true.obs;
  final _connectionType = ConnectivityResult.wifi.obs;

  bool get isConnected => _isConnected.value;
  ConnectivityResult get connectionType => _connectionType.value;

  @override
  void onInit() {
    super.onInit();
    _initConnectivity();
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectionStatus,
    );
  }

  @override
  void onClose() {
    _connectivitySubscription.cancel();
    super.onClose();
  }

  Future<void> _initConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } catch (e) {
      Logger.e('Could not check connectivity status: $e');
    }
  }

  void _updateConnectionStatus(ConnectivityResult result) {
    _connectionType.value = result;
    _isConnected.value = result != ConnectivityResult.none;

    if (_isConnected.value) {
      _onConnected();
    } else {
      _onDisconnected();
    }
  }

  void _onConnected() {
    Get.snackbar(
      'Connection Restored',
      'Internet connection is back',
      snackPosition: SnackPosition.TOP,
      duration: const Duration(seconds: 2),
    );

    // Trigger sync when connection is restored
    _triggerSync();
  }

  void _onDisconnected() {
    Get.snackbar(
      'No Internet',
      'Working offline. Changes will sync when connection is restored.',
      snackPosition: SnackPosition.TOP,
      duration: const Duration(seconds: 3),
    );
  }

  void _triggerSync() {
    // This will be called by the sync service
    // For now, just show a message that sync would be triggered
    Logger.i('Sync would be triggered here when connection is restored');
  }

  Future<bool> hasInternetConnection() async {
    final result = await _connectivity.checkConnectivity();
    return result != ConnectivityResult.none;
  }

  String get connectionTypeString {
    switch (_connectionType.value) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return 'Mobile Data';
      case ConnectivityResult.ethernet:
        return 'Ethernet';
      case ConnectivityResult.bluetooth:
        return 'Bluetooth';
      case ConnectivityResult.vpn:
        return 'VPN';
      case ConnectivityResult.other:
        return 'Other';
      case ConnectivityResult.none:
        return 'No Connection';
    }
  }
}

// Enhanced Sync Service
class SyncService extends GetxService {
  static SyncService get to => Get.find();

  final _isSyncing = false.obs;
  final _lastSyncTime = Rxn<DateTime>();
  final _pendingCount = 0.obs;

  bool get isSyncing => _isSyncing.value;
  DateTime? get lastSyncTime => _lastSyncTime.value;
  int get pendingCount => _pendingCount.value;

  Timer? _periodicSyncTimer;

  @override
  void onInit() {
    super.onInit();
    _startPeriodicSync();
    _updatePendingCount();
  }

  @override
  void onClose() {
    _periodicSyncTimer?.cancel();
    super.onClose();
  }

  void _startPeriodicSync() {
    _periodicSyncTimer = Timer.periodic(
      const Duration(minutes: 15),
      (_) => syncWhenConnected(),
    );
  }

  Future<void> syncWhenConnected() async {
    if (!ConnectivityService.to.isConnected || _isSyncing.value) {
      return;
    }

    await syncAll();
  }

  Future<void> syncAll() async {
    if (_isSyncing.value) return;

    _isSyncing.value = true;

    try {
      // Sync applications
      final result = await ApplicationRepository().syncApplications();

      if (result.isSuccess) {
        _lastSyncTime.value = DateTime.now();

        if (result.successCount > 0) {
          Get.snackbar(
            'Sync Complete',
            'Successfully synced ${result.successCount} applications',
            snackPosition: SnackPosition.TOP,
            duration: const Duration(seconds: 2),
          );
        }

        if (result.failureCount > 0) {
          Get.snackbar(
            'Partial Sync',
            '${result.failureCount} applications failed to sync',
            snackPosition: SnackPosition.TOP,
            duration: const Duration(seconds: 3),
          );
        }
      } else {
        Get.snackbar(
          'Sync Failed',
          result.error ?? 'Unknown error occurred',
          snackPosition: SnackPosition.TOP,
          duration: const Duration(seconds: 3),
        );
      }
    } catch (e) {
      Get.snackbar(
        'Sync Error',
        'Failed to sync: ${e.toString()}',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } finally {
      _isSyncing.value = false;
      _updatePendingCount();
    }
  }

  Future<void> _updatePendingCount() async {
    try {
      final applications = await ApplicationRepository().getApplications();
      final pending =
          applications
              .where(
                (app) =>
                    app.syncStatus == 'pending' ||
                    app.syncStatus == 'created' ||
                    app.syncStatus == 'updated',
              )
              .length;

      _pendingCount.value = pending;
    } catch (e) {
      Logger.e('Error updating pending count: $e');
    }
  }

  Future<void> forceSyncApplication(String applicationId) async {
    if (!ConnectivityService.to.isConnected) {
      Get.snackbar(
        'No Internet',
        'Cannot sync without internet connection',
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    try {
      // Implementation for syncing specific application
      Get.snackbar(
        'Syncing',
        'Syncing application...',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 1),
      );

      // Add specific sync logic here
    } catch (e) {
      Get.snackbar(
        'Sync Failed',
        'Failed to sync application: ${e.toString()}',
        snackPosition: SnackPosition.TOP,
      );
    }
  }
}
